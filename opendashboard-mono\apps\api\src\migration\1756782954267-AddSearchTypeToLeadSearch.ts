import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSearchTypeToLeadSearch1756782954267 implements MigrationInterface {
    name = 'AddSearchTypeToLeadSearch1756782954267'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`usersQuota\` \`usersQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))) STORED NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))","GENERATED_COLUMN","usersQuota","local","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`collaboratorsQuota\` \`collaboratorsQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))) STORED NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))","GENERATED_COLUMN","collaboratorsQuota","local","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`recordsQuota\` \`recordsQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))) STORED NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))","GENERATED_COLUMN","recordsQuota","local","billing_cycle"]);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["","GENERATED_COLUMN","recordsQuota","local","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`recordsQuota\` \`recordsQuota\` int NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["","GENERATED_COLUMN","collaboratorsQuota","local","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`collaboratorsQuota\` \`collaboratorsQuota\` int NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["","GENERATED_COLUMN","usersQuota","local","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`usersQuota\` \`usersQuota\` int NOT NULL`);
    }

}
