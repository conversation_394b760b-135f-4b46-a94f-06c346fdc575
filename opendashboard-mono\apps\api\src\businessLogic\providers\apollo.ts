import { LeadService } from "../../service/lead";
import { LeadUnlockService } from "../../service/leadUnlock";

export const processApolloPhoneReveal = async (webhookData: any) => {
    console.log('🔓 [APOLLO WEBHOOK] 🚀 Processing Apollo phone reveal webhook');
    console.log('🔓 [APOLLO WEBHOOK] - Webhook data:', JSON.stringify(webhookData, null, 2));
    
    try {
        const { people } = webhookData;
        
        if (!people || !Array.isArray(people)) {
            throw new Error('Invalid webhook data: missing people array');
        }
        
        const results = [];
        
        for (const person of people) {
            const { id: apolloId, phone_numbers, status, email } = person;
            
            console.log(`🔓 [APOLLO WEBHOOK] 📱 Processing person ${apolloId} with status: ${status}`);
            
            // Handle different status scenarios
            if (status !== 'success') {
                console.log(`🔓 [APOLLO WEBHOOK] ⚠️ Person ${apolloId} status: ${status} - checking for available data`);
                
                // Find lead to check if email was already unlocked
                const leadService = new LeadService();
                const lead = await leadService.findOne({ apolloId });
                
                let existingEmailData = false;
                if (lead && lead.apolloData) {
                    const currentPerson = (lead.apolloData as any).person || {};
                    const currentEmail = currentPerson.email;
                    existingEmailData = currentEmail && currentEmail !== '<EMAIL>';
                    console.log(`🔓 [APOLLO WEBHOOK] 🔍 Existing email check:`, {
                        hasExistingEmail: existingEmailData,
                        currentEmail: currentEmail
                    });
                }
                
                // 🔧 SMART FALLBACK: Check if email is available in webhook response
                const webhookEmail = email;
                const hasWebhookEmail = webhookEmail && webhookEmail !== '<EMAIL>';
                const shouldUnlockEmail = hasWebhookEmail && !existingEmailData;
                
                console.log(`🔓 [APOLLO WEBHOOK] 🔍 Webhook email check:`, {
                    hasWebhookEmail,
                    webhookEmail: webhookEmail,
                    shouldUnlockEmail,
                    reason: shouldUnlockEmail ? 'Email available in webhook, will unlock it' : 'No email to unlock'
                });
                
                // If we have email in webhook and no existing email, unlock it
                if (shouldUnlockEmail && lead) {
                    console.log(`🔓 [APOLLO WEBHOOK] 🎁 BONUS EMAIL UNLOCK: Phone failed but email available - unlocking email!`);
                    
                    // Smart data merging - preserve existing data, add email
                    const currentApolloData = lead.apolloData as any || {};
                    const currentPerson = currentApolloData.person || {};
                    
                    const updatedApolloData = {
                        ...currentApolloData,
                        person: {
                            ...currentPerson,
                            // Add the email from webhook
                            email: webhookEmail,
                            // Preserve all other existing person data
                            id: apolloId,
                            last_updated: new Date().toISOString()
                        }
                    };
                    
                    console.log(`🔓 [APOLLO WEBHOOK] 🔍 BONUS EMAIL UPDATE - Current apolloData:`, JSON.stringify(currentApolloData, null, 2));
                    console.log(`🔓 [APOLLO WEBHOOK] 🔍 BONUS EMAIL UPDATE - New apolloData:`, JSON.stringify(updatedApolloData, null, 2));
                    
                    try {
                        const updateResult = await leadService.update({ id: lead.id }, {
                            apolloData: updatedApolloData,
                            lastEnrichedAt: new Date()
                        });
                        console.log(`🔓 [APOLLO WEBHOOK] ✅ BONUS EMAIL UNLOCKED:`, updateResult);
                        
                        results.push({
                            apolloId,
                            status: 'partial_success',
                            reason: `Phone unlock failed but email unlocked successfully`,
                            hasPhoneData: false,
                            hasEmailData: true  // ✅ Email was unlocked!
                        });
                        continue;
                    } catch (updateError) {
                        console.error(`🔓 [APOLLO WEBHOOK] ❌ Bonus email unlock failed:`, updateError);
                    }
                }
                
                results.push({
                    apolloId,
                    status: 'failed',
                    reason: `Apollo returned status: ${status}`,
                    hasPhoneData: false,
                    hasEmailData: existingEmailData  // ✅ Preserve existing email status!
                });
                continue;
            }
            
            // Validate data quality
            const hasPhoneData = phone_numbers && Array.isArray(phone_numbers) && phone_numbers.length > 0;
            const hasEmailData = email && email !== '<EMAIL>';
            
            console.log(`🔓 [APOLLO WEBHOOK] 🔍 Data validation:`, {
                hasPhoneData,
                hasEmailData,
                phoneCount: hasPhoneData ? phone_numbers.length : 0,
                email: hasEmailData ? email : 'none'
            });
            
            // Find lead by Apollo ID
            const leadService = new LeadService();
            const lead = await leadService.findOne({ apolloId });
            
            if (!lead) {
                console.log(`🔓 [APOLLO WEBHOOK] ⚠️ No lead found for Apollo ID: ${apolloId}`);
                results.push({
                    apolloId,
                    status: 'failed',
                    reason: 'Lead not found in database',
                    hasPhoneData,
                    hasEmailData
                });
                continue;
            }
            
            console.log(`🔓 [APOLLO WEBHOOK] ✅ Found lead: ${lead.id}`);
            
            // Smart data merging - preserve existing data, add new data
            const currentApolloData = lead.apolloData as any || {};
            const currentPerson = currentApolloData.person || {};
            
            const updatedApolloData = {
                ...currentApolloData,
                person: {
                    ...currentPerson,
                    // Only add phone numbers if we have new ones
                    ...(hasPhoneData && { phone_numbers }),
                    // Only add email if we have a new one and it's better quality
                    ...(hasEmailData && (!currentPerson.email || email !== currentPerson.email) && { email }),
                    // Preserve all other existing person data
                    id: apolloId,
                    last_updated: new Date().toISOString()
                }
            };
            
            console.log(`🔓 [APOLLO WEBHOOK] 🔍 BEFORE UPDATE - Current apolloData:`, JSON.stringify(currentApolloData, null, 2));
            console.log(`🔓 [APOLLO WEBHOOK] 🔍 UPDATING WITH - New apolloData:`, JSON.stringify(updatedApolloData, null, 2));
            
            // Update the lead with enhanced error handling
            let updateResult;
            try {
                updateResult = await leadService.update({ id: lead.id }, {
                    apolloData: updatedApolloData,
                    lastEnrichedAt: new Date()
                });
                console.log(`🔓 [APOLLO WEBHOOK] 🔍 UPDATE RESULT:`, updateResult);
            } catch (updateError) {
                console.error(`🔓 [APOLLO WEBHOOK] ❌ Database update failed for lead ${lead.id}:`, updateError);
                results.push({
                    apolloId,
                    status: 'failed',
                    reason: 'Database update failed',
                    error: updateError.message,
                    hasPhoneData,
                    hasEmailData
                });
                continue;
            }
            
            console.log(`🔓 [APOLLO WEBHOOK] ✅ Lead ${lead.id} update completed`);
            
            // Verify the update was successful
            const updatedLead = await leadService.findOne({ id: lead.id });
            if (!updatedLead) {
                console.error(`🔓 [APOLLO WEBHOOK] ❌ Lead ${lead.id} not found after update`);
                results.push({
                    apolloId,
                    status: 'failed',
                    reason: 'Lead disappeared after update',
                    hasPhoneData,
                    hasEmailData
                });
                continue;
            }
            
            console.log(`🔓 [APOLLO WEBHOOK] 🔍 VERIFICATION - Updated lead data:`, JSON.stringify(updatedLead?.apolloData, null, 2));
            console.log(`🔓 [APOLLO WEBHOOK] 🔍 VERIFICATION - Phone numbers in updated data:`, (updatedLead?.apolloData as any)?.person?.phone_numbers);
            
            // Update unlock record with enhanced status
            const leadUnlockService = new LeadUnlockService();
            const existingUnlock = await leadUnlockService.findExistingUnlock(lead.workspaceId, lead.id, lead.createdById || 'system');
            
            if (existingUnlock) {
                try {
                    await leadUnlockService.updateUnlock(existingUnlock.id, {
                        isSuccessful: true,
                        apolloData: updatedApolloData
                    });
                    console.log(`🔓 [APOLLO WEBHOOK] ✅ Unlock marked as successful for lead ${lead.id}`);
                } catch (unlockError) {
                    console.error(`🔓 [APOLLO WEBHOOK] ⚠️ Failed to update unlock record:`, unlockError);
                    // Don't fail the whole process for unlock record issues
                }
            }
            
            // Track successful processing
            results.push({
                apolloId,
                status: 'success',
                hasPhoneData,
                hasEmailData,
                phoneCount: hasPhoneData ? phone_numbers.length : 0,
                dataQuality: {
                    phone: hasPhoneData ? 'high' : 'none',
                    email: hasEmailData ? 'verified' : 'none'
                }
            });
        }
        
        // Summary of all processing results
        const successCount = results.filter(r => r.status === 'success').length;
        const failedCount = results.filter(r => r.status === 'failed').length;
        
        console.log(`🔓 [APOLLO WEBHOOK] 📊 PROCESSING SUMMARY:`, {
            total: results.length,
            success: successCount,
            failed: failedCount,
            results
        });
        
        return {
            success: true,
            message: `Processed ${results.length} person(s): ${successCount} success, ${failedCount} failed`,
            processed: results.length,
            successful: successCount,
            failed: failedCount,
            details: results
        };
        
    } catch (error) {
        console.error('🔓 [APOLLO WEBHOOK] ❌ Critical error processing phone reveal:', error);
        throw error;
    }
};
