"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@ui/components/ui/dialog"
import { Button } from "@ui/components/ui/button"
import { Cross2Icon } from "@radix-ui/react-icons"
import { PhoneIcon, CheckIcon } from "@ui/components/icons/FontAwesomeRegular"
import { unlockLead } from "@ui/api/leads"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { useAlert } from "@ui/providers/alert"

interface UnlockPhoneModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  leadId: string
  onUnlockSuccess?: (unlockedLead: any) => void
}

export const UnlockPhoneModal = ({ open, onOpenChange, leadId, onUnlockSuccess }: UnlockPhoneModalProps) => {
  const { workspace } = useWorkspace()
  const { token } = useAuth()
  const { toast } = useAlert()
  
  const [unlocking, setUnlocking] = useState(false)
  const [unlocked, setUnlocked] = useState(false)

  const handleUnlock = async () => {
    if (!workspace?.workspace?.id || !token?.token) {
      toast.error("Authentication required")
      return
    }

    setUnlocking(true)
    try {
      const response = await unlockLead(token.token, workspace.workspace.id, leadId, {
        unlockType: 'phone'
      })

      if (response.error) {
        toast.error(response.error || "Failed to unlock phone")
        return
      }

      const data = response.data.data
      setUnlocked(true)
      
      // 🔧 SMART SUCCESS HANDLING: Handle different unlock scenarios
      if (data.alreadyUnlocked) {
        toast.info("Phone was already unlocked")
      } else {
        // Check if we got partial success (phone failed but email unlocked)
        const hasPhone = data.lead?.normalizedData?.phone && 
                        data.lead.normalizedData.phone !== '<EMAIL>';
        const hasEmail = data.lead?.normalizedData?.email && 
                        data.lead.normalizedData.email !== '<EMAIL>';
        
        if (!hasPhone && hasEmail) {
          // Phone failed but email was unlocked as bonus
          toast.success(`Email unlocked successfully! Phone not available, try again later. Used ${data.unlock.creditsUsed} credits.`)
        } else if (hasPhone) {
          // Phone unlocked successfully
          toast.success(`Phone unlocked successfully! Used ${data.unlock.creditsUsed} credits.`)
        } else {
          // Both failed
          toast.error("Phone unlock failed. Please try again later.")
        }
      }

      // Call the success callback to update parent component
      onUnlockSuccess?.(data.lead)
      
      // Close modal after a short delay
      setTimeout(() => {
        onOpenChange(false)
        setUnlocked(false)
      }, 2000)
      
    } catch (error) {
      console.error("Unlock error:", error)
      toast.error("Failed to unlock phone")
    } finally {
      setUnlocking(false)
    }
  }

  const handleClose = () => {
    if (!unlocking) {
      onOpenChange(false)
      setUnlocked(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[500px] max-w-[90vw] p-0 rounded-none border border-neutral-200 bg-white shadow-lg" hideCloseBtn>
        {/* Close Button */}
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-neutral-400 hover:text-neutral-600 cursor-pointer"
            onClick={handleClose}
            disabled={unlocking}
          >
            <Cross2Icon className="size-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-10 text-center">
          {/* Illustration */}
          <div className="mb-8 flex justify-center mt-8">
            {unlocked ? (
              <div className="w-24 h-14 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckIcon className="w-8 h-8 text-green-600" />
              </div>
            ) : (
              <div className="relative">
                {/* Smartphone */}
                <div className="w-24 h-14 border-2 border-neutral-900 rounded-lg relative">
                  <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-10 h-10 border border-neutral-900 rounded"></div>
                  <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-2.5 h-0.5 bg-neutral-900 rounded-full"></div>
                </div>
                
                {/* Padlock above phone */}
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                  <div className="w-8 h-8 border-2 border-neutral-900 rounded-lg relative">
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-2.5 h-4 border-2 border-neutral-900 rounded-t-lg"></div>
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-neutral-900 rounded-full"></div>
                  </div>
                </div>
                
                {/* Line connecting padlock to phone */}
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0.5 h-6 bg-neutral-900"></div>
                
                {/* Sparkles */}
                <div className="absolute -top-6 -left-3 w-2 h-2 bg-neutral-900 transform rotate-45"></div>
                <div className="absolute -top-4 -right-2 w-1.5 h-1.5 bg-neutral-900 transform rotate-45"></div>
                <div className="absolute -top-2 -left-2 w-1.5 h-1.5 bg-neutral-900 transform rotate-45"></div>
              </div>
            )}
          </div>

          {/* Heading */}
          <h2 className="text-2xl font-semibold text-black mb-4">
            {unlocked ? "Phone Unlocked!" : "Access phone number"}
          </h2>

          {/* Description */}
          <p className="text-xs text-muted-foreground mb-8 leading-relaxed">
            {unlocked 
              ? "The phone number has been successfully unlocked. You can now view and use this contact information."
              : "Phone details are hidden on free plans. Unlock now to connect directly."
            }
          </p>

          {/* Call to Action Button */}
          {unlocked ? (
            <div className="flex items-center justify-center gap-2 text-green-600">
              <CheckIcon className="w-5 h-5" />
              <span className="text-sm font-medium">Successfully Unlocked</span>
            </div>
          ) : (
            <Button
              onClick={handleUnlock}
              disabled={unlocking}
              className="w-full h-14 bg-neutral-900 hover:bg-neutral-800 text-white font-semibold rounded-full text-lg cursor-pointer disabled:opacity-50"
            >
              {unlocking ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Unlocking...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <PhoneIcon className="w-5 h-5" />
                  Unlock Phone
                </div>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
