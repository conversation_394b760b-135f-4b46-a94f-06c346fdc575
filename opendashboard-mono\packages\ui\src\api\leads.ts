import {apiUrl, BackendAPIResponse, normalizeResponse} from "@ui/api/common";
import {httpRequest} from "@ui/utils/http";
import {
    Lead,
    SearchFilters,
    SearchPagination,
    SearchLeadsRequest,
    SearchLeadsResponse,
    UnlockLeadResponse,
    SavedSearch,
    VoteFeedbackOptions
} from "../typings/lead";

// ============================================================================
// SEARCH ENDPOINTS
// ============================================================================

// 1. People Search
export const searchPeopleLeads = async (
    token: string, 
    workspaceId: string, 
    body: SearchLeadsRequest
) => {
    console.log(`🔍 [API DEBUG] searchPeopleLeads called`);
    console.log(`🔍 [API DEBUG] Workspace ID: ${workspaceId}`);
    console.log(`🔍 [API DEBUG] Request body:`, JSON.stringify(body, null, 2));
    console.log(`🔍 [API DEBUG] Token exists: ${!!token}`);
    
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/people/search`;
    console.log(`🔍 [API DEBUG] Endpoint: ${endpoint}`);
    console.log(`🔍 [API DEBUG] Making HTTP request...`);
    
    const response = await httpRequest("post", endpoint, headers, body);
    
    console.log(`🔍 [API DEBUG] HTTP response received:`, {
        status: response.status,
        hasData: !!response.data,
        responseKeys: response.data ? Object.keys(response.data) : []
    });
    
    const normalizedResponse = normalizeResponse(response) as BackendAPIResponse<SearchLeadsResponse>;
    
    console.log(`🔍 [API DEBUG] Normalized response:`, {
        hasError: !!normalizedResponse.error,
        error: normalizedResponse.error,
        hasData: !!normalizedResponse.data,
        dataKeys: normalizedResponse.data ? Object.keys(normalizedResponse.data) : []
    });
    
    return normalizedResponse;
};

// 2. Company Search
export const searchCompanyLeads = async (
    token: string, 
    workspaceId: string, 
    body: SearchLeadsRequest
) => {
    console.log(`🔍 [API DEBUG] searchCompanyLeads called`);
    console.log(`🔍 [API DEBUG] Workspace ID: ${workspaceId}`);
    console.log(`🔍 [API DEBUG] Request body:`, JSON.stringify(body, null, 2));
    
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    
    console.log(`🔍 [API DEBUG] Token exists: ${!!token}`);
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/companies/search`;
    console.log(`🔍 [API DEBUG] Endpoint: ${endpoint}`);
    console.log(`🔍 [API DEBUG] Making HTTP request...`);
    
    const response = await httpRequest("post", endpoint, headers, body);
    
    console.log(`🔍 [API DEBUG] HTTP response received:`, response);
    const normalizedResponse = normalizeResponse(response) as BackendAPIResponse<SearchLeadsResponse>;
    console.log(`🔍 [API DEBUG] Normalized response:`, normalizedResponse);
    
    return normalizedResponse;
};

// 3. Company Search with Bulk Enrichment
export const searchCompanyLeadsWithBulkEnrichment = async (
    token: string, 
    workspaceId: string, 
    body: SearchLeadsRequest & {
        customFilters?: {
            companyDomains?: string[];
        };
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/companies/search-with-bulk-enrichment`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<SearchLeadsResponse>;
};

// 4. People Search with Bulk Enrichment
export const searchPeopleLeadsWithBulkEnrichment = async (
    token: string, 
    workspaceId: string, 
    body: SearchLeadsRequest & {
        customFilters?: {
            emails?: string[];
        };
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/people/search-with-bulk-enrichment`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<SearchLeadsResponse>;
};

// ============================================================================
// DATA RETRIEVAL ENDPOINTS
// ============================================================================

// 5. Get My Leads (People)
export const getMyLeads = async (
    token: string, 
    workspaceId: string, 
    params: {
        page?: number;
        limit?: number;
        search?: string;
        filters?: string;
    } = {}
) => {
    console.log('🔍 [FRONTEND MY-LEADS] ==========================================');
    console.log('🔍 [FRONTEND MY-LEADS] 🚀 STARTING MY-LEADS REQUEST');
    console.log('🔍 [FRONTEND MY-LEADS] ==========================================');
    console.log('🔍 [FRONTEND MY-LEADS] Workspace ID:', workspaceId);
    console.log('🔍 [FRONTEND MY-LEADS] Token exists:', !!token);
    console.log('🔍 [FRONTEND MY-LEADS] Params:', JSON.stringify(params, null, 2));
    
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.filters) queryParams.append('filters', params.filters);
    
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/my-leads?${queryParams.toString()}`;
    console.log('🔍 [FRONTEND MY-LEADS] Endpoint:', endpoint);
    console.log('🔍 [FRONTEND MY-LEADS] Headers:', JSON.stringify(headers, null, 2));
    
    console.log('🔍 [FRONTEND MY-LEADS] 📤 SENDING HTTP REQUEST...');
    
    try {
        const response = await httpRequest("get", endpoint, headers);
        
        console.log('🔍 [FRONTEND MY-LEADS] 📥 HTTP RESPONSE RECEIVED:');
        console.log('🔍 [FRONTEND MY-LEADS] - Status:', response.status);
        console.log('🔍 [FRONTEND MY-LEADS] - Has data:', !!response.data);
        console.log('🔍 [FRONTEND MY-LEADS] - Response keys:', response.data ? Object.keys(response.data) : []);
        console.log('🔍 [FRONTEND MY-LEADS] - Raw response:', JSON.stringify(response, null, 2));
        
        const normalizedResponse = normalizeResponse(response) as BackendAPIResponse<{
            leads: Lead[];
            totalCount: number;
            hasNextPage: boolean;
        }>;
        
        console.log('🔍 [FRONTEND MY-LEADS] 📊 NORMALIZED RESPONSE:');
        console.log('🔍 [FRONTEND MY-LEADS] - Has error:', !!normalizedResponse.error);
        console.log('🔍 [FRONTEND MY-LEADS] - Error:', normalizedResponse.error);
        console.log('🔍 [FRONTEND MY-LEADS] - Has data:', !!normalizedResponse.data);
        console.log('🔍 [FRONTEND MY-LEADS] - Data keys:', normalizedResponse.data ? Object.keys(normalizedResponse.data) : []);
        
        if (normalizedResponse.data?.data) {
            console.log('🔍 [FRONTEND MY-LEADS] 📋 MY-LEADS DATA:');
            console.log('🔍 [FRONTEND MY-LEADS] - Total count:', normalizedResponse.data.data.totalCount);
            console.log('🔍 [FRONTEND MY-LEADS] - Has next page:', normalizedResponse.data.data.hasNextPage);
            console.log('🔍 [FRONTEND MY-LEADS] - Leads count:', normalizedResponse.data.data.leads?.length || 0);
            
            if (normalizedResponse.data.data.leads && normalizedResponse.data.data.leads.length > 0) {
                console.log('🔍 [FRONTEND MY-LEADS] 📧 LEADS DATA:');
                normalizedResponse.data.data.leads.forEach((lead, index) => {
                    console.log(`🔍 [FRONTEND MY-LEADS] Lead ${index + 1}:`);
                    console.log(`🔍 [FRONTEND MY-LEADS] - ID:`, lead.id);
                    console.log(`🔍 [FRONTEND MY-LEADS] - Name:`, lead.name);
                    console.log(`🔍 [FRONTEND MY-LEADS] - Is unlocked:`, lead.isUnlocked);
                    console.log(`🔍 [FRONTEND MY-LEADS] - Has normalized data:`, !!lead.normalizedData);
                    console.log(`🔍 [FRONTEND MY-LEADS] - Has Apollo data:`, !!lead.apolloData);
                    
                    if (lead.normalizedData) {
                        console.log(`🔍 [FRONTEND MY-LEADS] - Email:`, lead.normalizedData.email);
                        console.log(`🔍 [FRONTEND MY-LEADS] - Phone:`, lead.normalizedData.phone);
                        console.log(`🔍 [FRONTEND MY-LEADS] - Email visible:`, lead.normalizedData.isEmailVisible);
                        console.log(`🔍 [FRONTEND MY-LEADS] - Phone visible:`, lead.normalizedData.isPhoneVisible);
                    }
                    
                    if (lead.apolloData) {
                        console.log(`🔍 [FRONTEND MY-LEADS] - Apollo data keys:`, Object.keys(lead.apolloData));
                    }
                });
            } else {
                console.log('🔍 [FRONTEND MY-LEADS] ⚠️ No leads found in response');
            }
        }
        
        console.log('🔍 [FRONTEND MY-LEADS] ✅ MY-LEADS REQUEST COMPLETED');
        console.log('🔍 [FRONTEND MY-LEADS] ==========================================');
        
        return normalizedResponse;
        
    } catch (error) {
        console.log('🔍 [FRONTEND MY-LEADS] ❌ MY-LEADS REQUEST FAILED:');
        console.log('🔍 [FRONTEND MY-LEADS] - Error:', error);
        console.log('🔍 [FRONTEND MY-LEADS] - Error message:', error.message);
        console.log('🔍 [FRONTEND MY-LEADS] - Error stack:', error.stack);
        console.log('🔍 [FRONTEND MY-LEADS] ==========================================');
        
        throw error;
    }
};

// 6. Get My Company Leads
export const getMyCompanyLeads = async (
    token: string, 
    workspaceId: string, 
    params: {
        page?: number;
        limit?: number;
        search?: string;
        filters?: string;
    } = {}
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.filters) queryParams.append('filters', params.filters);
    
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/companies/my-leads?${queryParams.toString()}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        leads: Lead[];
        totalCount: number;
        hasNextPage: boolean;
    }>;
};

// ============================================================================
// SAVED SEARCH ENDPOINTS
// ============================================================================

// 7. Get Saved Searches
export const getSavedSearches = async (
    token: string, 
    workspaceId: string, 
    params: {
        page?: number;
        limit?: number;
        search?: string;
        searchType?: 'people' | 'company';
    } = {}
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.searchType) queryParams.append('searchType', params.searchType);
    
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/saved-searches?${queryParams.toString()}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        searches: SavedSearch[];
        totalCount: number;
    }>;
};

// 8. Save Search
export const saveSearch = async (
    token: string, 
    workspaceId: string, 
    body: {
        name: string;
        description?: string;
        filters: SearchFilters;
        searchId?: string;
        searchType?: 'people' | 'company';
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/saved-searches`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        savedSearch: SavedSearch;
    }>;
};

// 9. Get Saved Search
export const getSavedSearch = async (
    token: string, 
    workspaceId: string, 
    searchId: string
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/saved-searches/${searchId}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        savedSearch: SavedSearch;
    }>;
};

// 10. Update Saved Search
export const updateSavedSearch = async (
    token: string, 
    workspaceId: string, 
    searchId: string, 
    body: Partial<{
        name: string;
        description: string;
        filters: SearchFilters;
    }>
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/saved-searches/${searchId}`;
    const response = await httpRequest("patch", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        savedSearch: SavedSearch;
    }>;
};

// 11. Delete Saved Search
export const deleteSavedSearch = async (
    token: string, 
    workspaceId: string, 
    searchId: string
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/saved-searches/${searchId}`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
    }>;
};

// 12. Execute Saved Search
export const executeSavedSearch = async (
    token: string, 
    workspaceId: string, 
    searchId: string, 
    body: {
        pagination: SearchPagination;
        searchType: 'people' | 'company';
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/saved-searches/${searchId}/execute`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<SearchLeadsResponse>;
};

// ============================================================================
// LEAD MANAGEMENT ENDPOINTS
// ============================================================================

// 13. Get Lead
export const getLead = async (
    token: string, 
    workspaceId: string, 
    leadId: string
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<Lead>;
};

// 14. Update Lead
export const updateLead = async (  token: string,  workspaceId: string,  leadId: string, 
    body: Partial<{
        name: string;
        email: string;
        companyDomain: string;
        normalizedData: Partial<Lead['normalizedData']>;
    }>
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}`;
    const response = await httpRequest("patch", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        lead: Lead;
    }>;
};

// 15. Delete Lead
export const deleteLead = async (
    token: string, 
    workspaceId: string, 
    leadId: string
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}`;
    const response = await httpRequest("delete", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
    }>;
};

// 16. Unlock Lead
export const unlockLead = async (
    token: string, 
    workspaceId: string, 
    leadId: string, 
    body: {
        unlockType: 'email' | 'phone';  // 🔧 SIMPLIFY: Only email and phone, phone does everything
        source?: string;
    }
) => {
    console.log('🔍 [FRONTEND UNLOCK] ==========================================');
    console.log('🔍 [FRONTEND UNLOCK] 🚀 STARTING UNLOCK REQUEST');
    console.log('🔍 [FRONTEND UNLOCK] ==========================================');
    console.log('🔍 [FRONTEND UNLOCK] Lead ID:', leadId);
    console.log('🔍 [FRONTEND UNLOCK] Workspace ID:', workspaceId);
    console.log('🔍 [FRONTEND UNLOCK] Unlock Type:', body.unlockType);
    console.log('🔍 [FRONTEND UNLOCK] Source:', body.source);
    console.log('🔍 [FRONTEND UNLOCK] Token exists:', !!token);
    console.log('🔍 [FRONTEND UNLOCK] Request body:', JSON.stringify(body, null, 2));
    
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}/unlock`;
    console.log('🔍 [FRONTEND UNLOCK] Endpoint:', endpoint);
    console.log('🔍 [FRONTEND UNLOCK] Headers:', JSON.stringify(headers, null, 2));
    
    console.log('🔍 [FRONTEND UNLOCK] 📤 SENDING HTTP REQUEST...');
    
    try {
        const response = await httpRequest("post", endpoint, headers, body);
        
        console.log('🔍 [FRONTEND UNLOCK] 📥 HTTP RESPONSE RECEIVED:');
        console.log('🔍 [FRONTEND UNLOCK] - Status:', response.status);
        console.log('🔍 [FRONTEND UNLOCK] - Has data:', !!response.data);
        console.log('🔍 [FRONTEND UNLOCK] - Response keys:', response.data ? Object.keys(response.data) : []);
        console.log('🔍 [FRONTEND UNLOCK] - Raw response:', JSON.stringify(response, null, 2));
        
        const normalizedResponse = normalizeResponse(response) as BackendAPIResponse<UnlockLeadResponse>;
        
        console.log('🔍 [FRONTEND UNLOCK] 📊 NORMALIZED RESPONSE:');
        console.log('🔍 [FRONTEND UNLOCK] - Has error:', !!normalizedResponse.error);
        console.log('🔍 [FRONTEND UNLOCK] - Error:', normalizedResponse.error);
        console.log('🔍 [FRONTEND UNLOCK] - Has data:', !!normalizedResponse.data);
        console.log('🔍 [FRONTEND UNLOCK] - Data keys:', normalizedResponse.data ? Object.keys(normalizedResponse.data) : []);
        
        if (normalizedResponse.data?.data) {
            console.log('🔍 [FRONTEND UNLOCK] 📋 UNLOCK RESPONSE DATA:');
            console.log('🔍 [FRONTEND UNLOCK] - Already unlocked:', normalizedResponse.data.data.alreadyUnlocked);
            console.log('🔍 [FRONTEND UNLOCK] - Has lead data:', !!normalizedResponse.data.data.lead);
            console.log('🔍 [FRONTEND UNLOCK] - Has unlock data:', !!normalizedResponse.data.data.unlock);
            
            if (normalizedResponse.data.data.lead) {
                console.log('🔍 [FRONTEND UNLOCK] 📧 LEAD DATA:');
                console.log('🔍 [FRONTEND UNLOCK] - Lead ID:', normalizedResponse.data.data.lead.id);
                console.log('🔍 [FRONTEND UNLOCK] - Is unlocked:', normalizedResponse.data.data.lead.isUnlocked);
                console.log('🔍 [FRONTEND UNLOCK] - Has normalized data:', !!normalizedResponse.data.data.lead.normalizedData);
                console.log('🔍 [FRONTEND UNLOCK] - Has Apollo data:', !!normalizedResponse.data.data.lead.apolloData);
                
                if (normalizedResponse.data.data.lead.normalizedData) {
                    console.log('🔍 [FRONTEND UNLOCK] 📊 NORMALIZED DATA:');
                    console.log('🔍 [FRONTEND UNLOCK] - Email:', normalizedResponse.data.data.lead.normalizedData.email);
                    console.log('🔍 [FRONTEND UNLOCK] - Phone:', normalizedResponse.data.data.lead.normalizedData.phone);
                    console.log('🔍 [FRONTEND UNLOCK] - Email visible:', normalizedResponse.data.data.lead.normalizedData.isEmailVisible);
                    console.log('🔍 [FRONTEND UNLOCK] - Phone visible:', normalizedResponse.data.data.lead.normalizedData.isPhoneVisible);
                }
                
                if (normalizedResponse.data.data.lead.apolloData) {
                    console.log('🔍 [FRONTEND UNLOCK] 📊 APOLLO DATA:');
                    console.log('🔍 [FRONTEND UNLOCK] - Apollo data keys:', Object.keys(normalizedResponse.data.data.lead.apolloData));
                }
            }
        }
        
        console.log('🔍 [FRONTEND UNLOCK] ✅ UNLOCK REQUEST COMPLETED');
        console.log('🔍 [FRONTEND UNLOCK] ==========================================');
        
        return normalizedResponse;
        
    } catch (error) {
        console.log('🔍 [FRONTEND UNLOCK] ❌ UNLOCK REQUEST FAILED:');
        console.log('🔍 [FRONTEND UNLOCK] - Error:', error);
        console.log('🔍 [FRONTEND UNLOCK] - Error message:', error.message);
        console.log('🔍 [FRONTEND UNLOCK] - Error stack:', error.stack);
        console.log('🔍 [FRONTEND UNLOCK] ==========================================');
        
        throw error;
    }
};

// ============================================================================
// VOTING & FEEDBACK ENDPOINTS
// ============================================================================

// 17. Vote Lead
export const voteLead = async (
    token: string, 
    workspaceId: string, 
    leadId: string, 
    body: {
        vote: 'up' | 'down';
        feedback: string;
        feedbackType: 'predefined' | 'custom';
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}/vote`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
    }>;
};

// 18. Get Vote Feedback Options
export const getVoteFeedbackOptions = async (
    token: string, 
    workspaceId: string
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/vote-feedback-options`;
    const response = await httpRequest("get", endpoint, headers);
    return normalizeResponse(response) as BackendAPIResponse<VoteFeedbackOptions>;
};

// ============================================================================
// BULK OPERATIONS ENDPOINTS
// ============================================================================

// 19. Bulk Unlock Leads
export const bulkUnlockLeads = async (
    token: string, 
    workspaceId: string, 
    body: {
        leadIds: string[];
        unlockType?: 'full' | 'partial';
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/bulk-unlock`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        results: UnlockLeadResponse[];
        successCount: number;
        failureCount: number;
    }>;
};

// 20. Bulk Enrich Leads
export const bulkEnrichLeads = async (
    token: string, 
    workspaceId: string, 
    body: {
        leadIds: string[];
        enrichmentType?: 'full' | 'partial';
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/bulk-enrich`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        results: Lead[];
        successCount: number;
        failureCount: number;
    }>;
};

// ============================================================================
// LEAD INTEGRATION ENDPOINTS
// ============================================================================

// 21. Add Lead to Segment
export const addLeadToSegment = async (
    token: string, 
    workspaceId: string, 
    leadId: string, 
    body: {
        name: string;
        databaseId?: string;
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}/add-to-segment`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
        segmentId: string;
    }>;
};

// 22. Add Lead to Workflow
export const addLeadToWorkflow = async (
    token: string, 
    workspaceId: string, 
    leadId: string, 
    body: {
        workflowId: string;
        triggerType?: 'manual' | 'immediate';
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}/add-to-workflow`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
        workflowInstanceId: string;
    }>;
};

// 23. Add Lead to Database
export const addLeadToDatabase = async (
    token: string, 
    workspaceId: string, 
    leadId: string, 
    body: {
        targetDatabaseId: string;
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}/add-to-database`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
        externalId: string;
    }>;
};

// 24. Send Email to Lead
export const sendEmailToLead = async (
    token: string, 
    workspaceId: string, 
    leadId: string, 
    body: {
        subject: string;
        body: string;
        template?: string;
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/${leadId}/send-email`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        success: boolean;
        emailId: string;
        sentAt: string;
    }>;
};

// ============================================================================
// UTILITY ENDPOINTS
// ============================================================================

// 25. Export Leads
export const exportLeads = async (
    token: string, 
    workspaceId: string, 
    body: {
        leadIds: string[];
        format: 'csv' | 'json' | 'xlsx';
        includeFullData: boolean;
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/export`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        downloadUrl: string;
        format: string;
        recordCount: number;
        expiresAt: string;
    }>;
};

// 26. Create Lead in Apollo
export const createLeadInApollo = async (
    token: string, 
    workspaceId: string, 
    body: {
        firstName: string;
        lastName: string;
        email: string;
        companyDomain?: string;
        jobTitle?: string;
    }
) => {
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
    };
    const endpoint = `${apiUrl()}/workspaces/${workspaceId}/leads/create-in-apollo`;
    const response = await httpRequest("post", endpoint, headers, body);
    return normalizeResponse(response) as BackendAPIResponse<{
        lead: Lead;
        apolloId: string;
    }>;
};
