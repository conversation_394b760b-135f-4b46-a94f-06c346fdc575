import { Lead, LeadType, LeadSource, NormalizedLeadData, ApolloPersonData, ApolloCompanyData } from "../entity/Lead";
import { LeadSearch, SearchFilters, SearchPagination, SearchResultMetadata } from "../entity/LeadSearch";
import { LeadUnlock, UnlockType, UnlockSource, UnlockMetadata } from "../entity/LeadUnlock";
import { ExecuteWorkspaceIntegrationActionNoAuth, GetMyWorkspace, HasPermission, WorkspacePermission } from "./workspace";
import { NotfoundError, RequiredParameterError, ErrorMessage, UnauthorizedError, BadRequestError, ServerProcessingError, InvalidParameterError } from "../errors/AppError";
import { createHash } from "crypto";
import { LeadService } from "../service/lead";
import { LeadSearchService } from "../service/leadSearch";
import { LeadUnlockService } from "../service/leadUnlock";
import { dbDataSource } from "../connection/db";
import { In, Not } from "typeorm";
import { GetMyDatabases, CreateDatabase, AddRecords, GetMyDatabase } from "./database";
import { GetWorkflows, CreateWorkflowInstance } from "./workflow";
import { SendEmail, SendEmailWithContent } from "./email";
import { OnDuplicateAction } from "../service/record";
import { RecordValues } from "@repo/app-db-utils/dist/typings/db";
import { consoleLog } from "./logtail";
import { ConnectionAPI, IntegrationCredentials } from '@opendashboard-inc/integration-core-api';
import { apiUrl, isLocal } from "../config";

// Types for API interfaces
export interface SearchLeadsRequest {
    filters: SearchFilters;
    pagination: SearchPagination;
    excludeMyLeads?: boolean;
}

export interface LeadFilterOptions {
    page: number;
    limit: number;
    search?: string;
    filters?: Record<string, string | string[] | number | boolean>;
}

export interface SearchLeadsResponse {
    leads: Lead[];
    totalCount: number;
    hasNextPage: boolean;
    searchId: string;
    filters: SearchFilters;
    metadata: SearchResultMetadata;
}

export interface UnlockLeadRequest {
    unlockType: UnlockType;
    source: UnlockSource;
}

export interface SaveSearchRequest {
    name: string;
    description?: string;
    filters: SearchFilters;
    searchId?: string;
    searchType?: 'people' | 'company';
}

export interface LeadVoteRequest {
    vote: 'up' | 'down';
    feedback: string;
    feedbackType: 'predefined' | 'custom';
}

export interface LeadVoteFeedback {
    userId: string;
    vote: 'up' | 'down';
    feedback: string;
    feedbackType: 'predefined' | 'custom';
    votedAt: Date;
}

export interface LeadMeta {
    votes?: LeadVoteFeedback[];
    [key: string]: string | number | boolean | string[] | number[] | LeadVoteFeedback[] | undefined;
}

// Predefined feedback options for voting
export const PREDEFINED_FEEDBACK_OPTIONS = {
    up: [
        'High quality lead',
        'Good company fit',
        'Relevant job title',
        'Active on LinkedIn',
        'Company is growing',
        'Good location match',
        'Recent job change',
        'High engagement potential'
    ],
    down: [
        'Poor data quality',
        'Wrong company size',
        'Outdated information',
        'Low engagement potential',
        'Wrong location',
        'Company not relevant',
        'Job title mismatch',
        'Inactive profile'
    ]
} as const;


export const DEFAULT_PAGINATION = {
    PAGE: 1,
    LIMIT: 50,
    MAX_LIMIT: 50
} as const;



export interface BulkUnlockRequest {
    unlockType: UnlockType;
    source: UnlockSource;
}

export interface ExportRequest {
    format: 'csv' | 'json' | 'xlsx';
    includeFullData: boolean;
}

export interface LeadUpdateData {
    name?: string;
    email?: string;
    phone?: string;
    companyDomain?: string;
    isUnlocked?: boolean;
    lastEnrichedAt?: Date;
    meta?: Record<string, string | number | boolean | string[] | number[] | LeadVoteFeedback[]>;
}

export interface SearchUpdateData {
    name?: string;
    description?: string;
    filters?: SearchFilters;
    isSaved?: boolean;
}


export interface LeadResponse<T = any> {
    success: boolean;
    data: T;
    message?: string;
    error?: string;
}

// Pagination response interface (used in our functions)
export interface PaginatedResponse<T> {
    data: T[];
    totalCount: number;
    page: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}


function generateSearchHash(filters: SearchFilters): string {

    
    // Clean filters before hashing - remove excludeMyLeads
    const cleanFilters = { ...filters };
    delete cleanFilters.excludeMyLeads;
    
    const searchData = {
        filters: cleanFilters
       
    };
    
    const jsonString = JSON.stringify(searchData);
    const hash = createHash('sha256').update(jsonString).digest('hex');
    
    console.log(`🔍 [HASH DEBUG] Generating search hash:`, {
        inputData: searchData,
        originalFilters: filters,
        cleanedFilters: cleanFilters,
        jsonString: jsonString,
        generatedHash: hash
    });
    
    return hash;
}


function normalizeApolloPersonData(apolloData: ApolloPersonData): NormalizedLeadData {
    return {
        id: apolloData.id || '',
        name: apolloData.name || `${apolloData.first_name || ''} ${apolloData.last_name || ''}`.trim(),
        email: apolloData.email,
        phone: apolloData.phone,
        jobTitle: apolloData.title,
        company: apolloData.organization?.name,
        companyDomain: apolloData.organization?.primary_domain,
        linkedinUrl: apolloData.linkedin_url,
        photoUrl: apolloData.photo_url,
        location: {
            country: apolloData.country,
            state: apolloData.state,
            city: apolloData.city
        },
        isEmailVisible: false,
        isPhoneVisible: false,
        confidence: apolloData.extrapolated_email_confidence
    };
}

// Helper function to normalize Apollo company data
function normalizeApolloCompanyData(apolloData: ApolloCompanyData): NormalizedLeadData {
    return {
        id: apolloData.id || '',
        name: apolloData.name || '',
        email: undefined, // Companies don't have emails typically
        phone: apolloData.phone,
        jobTitle: undefined,
        company: apolloData.name,
        companyDomain: apolloData.primary_domain,
        linkedinUrl: apolloData.linkedin_url,
        photoUrl: apolloData.logo_url,
        location: undefined, // Companies might need different location handling
        isEmailVisible: false,
        isPhoneVisible: false,
        confidence: undefined
    };
}

// Search for people leads
export const SearchPeopleLeads = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
        // All validation removed - trust user input and let business logic handle validation
    
    return await SearchLeadsByType(userId, workspaceId, request, LeadType.Person);
};

// Search for company leads
export const SearchCompanyLeads = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
    // All validation removed - trust user input and let business logic handle validation
    
    return await SearchLeadsByType(userId, workspaceId, request, LeadType.Company);
};



// Internal search function that handles both types
async function SearchLeadsByType(userId: string, workspaceId: string, request: SearchLeadsRequest, leadType: LeadType): Promise<SearchLeadsResponse> {
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    console.log(`🔍 [SEARCH DEBUG] 🚀 NEW SEARCH REQUEST STARTED`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    console.log(`🔍 [SEARCH DEBUG] User ID: ${userId}`);
    console.log(`🔍 [SEARCH DEBUG] Workspace ID: ${workspaceId}`);
    console.log(`🔍 [SEARCH DEBUG] Lead Type: ${leadType}`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { filters, pagination, excludeMyLeads } = request;
    
    // 🛡️ SAFETY NET STRATEGY: If bugs cause Apollo calls, we get different results
    // This ensures M1-M100 becomes M101-M200, expanding our database
    // Users never see duplicate results, even if system has bugs
    
    // Log the incoming request details
    console.log(`🔍 [SEARCH DEBUG] 📥 INCOMING REQUEST DETAILS:`);
    console.log(`🔍 [SEARCH DEBUG] - Filters: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [SEARCH DEBUG] - Pagination: ${JSON.stringify(pagination, null, 2)}`);
    console.log(`🔍 [SEARCH DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
    console.log(`🔍 [SEARCH DEBUG] - User ID: ${userId}`);
    console.log(`🔍 [SEARCH DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    const searchHash = generateSearchHash(filters);
    console.log(`🔍 [SEARCH DEBUG] 🔐 GENERATED SEARCH HASH: ${searchHash}`);
    console.log(`🔍 [SEARCH DEBUG] Hash Input (filters only): ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [SEARCH DEBUG] Note: excludeMyLeads (${excludeMyLeads}) is NOT part of hash`);
    console.log(`🔍 [SEARCH DEBUG] This ensures same search hits cache regardless of excludeMyLeads value`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    // Add detailed hash debugging
    console.log(`🔍 [HASH DEBUG] ==========================================`);
    console.log(`🔍 [HASH DEBUG] 🔍 DETAILED HASH ANALYSIS`);
    console.log(`🔍 [HASH DEBUG] - Raw filters received: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [HASH DEBUG] - Generated hash: ${searchHash}`);
    console.log(`🔍 [HASH DEBUG] - Hash length: ${searchHash.length}`);
    console.log(`🔍 [HASH DEBUG] - Requested page: ${pagination.page}`);
    console.log(`🔍 [HASH DEBUG] - Requested limit: ${pagination.limit}`);
    console.log(`🔍 [HASH DEBUG] ==========================================`);
    
    // Add detailed filter analysis
    console.log(`🔍 [SEARCH DEBUG] 🔍 DETAILED FILTER ANALYSIS:`);
    console.log(`🔍 [SEARCH DEBUG] - Person Job Titles: ${filters.person?.jobTitles?.join(', ') || 'None'}`);
    console.log(`🔍 [SEARCH DEBUG] - Person Location: ${filters.person?.location?.join(', ') || 'None'}`);
    console.log(`🔍 [SEARCH DEBUG] - Company Industry: ${filters.company?.industry?.join(', ') || 'None'}`);
    console.log(`🔍 [SEARCH DEBUG] - Company Size: ${filters.company?.companySize?.join(', ') || 'None'}`);
    console.log(`🔍 [SEARCH DEBUG] - Signals: ${JSON.stringify(filters.signals || {}, null, 2)}`);
    console.log(`🔍 [SEARCH DEBUG] - Custom Filters: ${JSON.stringify(filters.customFilters || {}, null, 2)}`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    const leadSearchService = new LeadSearchService();
    const leadService = new LeadService();
    
    console.log(`🔍 [SEARCH DEBUG] 🔍 LOOKING FOR EXISTING SEARCH IN DATABASE:`);
    console.log(`🔍 [SEARCH DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔍 [SEARCH DEBUG] - Search Hash: ${searchHash}`);
    console.log(`🔍 [SEARCH DEBUG] - This ensures each workspace has isolated search cache`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    let existingSearch = await leadSearchService.findByWorkspaceAndHash(workspaceId, searchHash);
    
    console.log(`🔍 [CACHE DEBUG] ==========================================`);
    console.log(`🔍 [CACHE DEBUG] 🔍 CACHE LOOKUP RESULT`);
    console.log(`🔍 [CACHE DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔍 [CACHE DEBUG] - Search Hash: ${searchHash}`);
    console.log(`🔍 [CACHE DEBUG] - Found: ${!!existingSearch}`);
    console.log(`🔍 [CACHE DEBUG] - Search ID: ${existingSearch?.id || 'N/A'}`);
    console.log(`🔍 [CACHE DEBUG] - Last Executed: ${existingSearch?.lastExecutedAt || 'N/A'}`);
    console.log(`🔍 [CACHE DEBUG] - Result Count: ${existingSearch?.resultIds?.length || 0}`);
    console.log(`🔍 [CACHE DEBUG] - Condition Met: ${!!(existingSearch && existingSearch.lastExecutedAt)}`);
    console.log(`🔍 [CACHE DEBUG] - Search Type: ${existingSearch?.searchType || 'N/A'}`);
    console.log(`🔍 [CACHE DEBUG] - Is Saved: ${existingSearch?.isSaved || false}`);
    console.log(`🔍 [CACHE DEBUG] - Is Active: ${existingSearch?.isActive || false}`);
    console.log(`🔍 [CACHE DEBUG] ==========================================`);
    
    // If no existing search found, log potential reasons
    if (!existingSearch) {
        console.log(`🔍 [SEARCH DEBUG] ❌ CACHE MISS! No existing search found`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        console.log(`🔍 [SEARCH DEBUG] 📋 POSSIBLE REASONS FOR CACHE MISS:`);
        console.log(`🔍 [SEARCH DEBUG] 1. First time searching with these exact filters in this workspace`);
        console.log(`🔍 [SEARCH DEBUG] 2. Filter structure changed between searches`);
        console.log(`🔍 [SEARCH DEBUG] 3. Hash generation issue`);
        console.log(`🔍 [SEARCH DEBUG] 4. Database search issue`);
        console.log(`🔍 [SEARCH DEBUG] 5. Different workspace (each workspace has isolated cache)`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        // Log all searches for this workspace to help debug
        const allSearches = await leadSearchService.findByWorkspaceAndFilters(workspaceId, {});
        console.log(`🔍 [CACHE MISS DEBUG] ==========================================`);
        console.log(`🔍 [CACHE MISS DEBUG] 📊 ALL SEARCHES IN THIS WORKSPACE`);
        console.log(`🔍 [CACHE MISS DEBUG] - Workspace ID: ${workspaceId}`);
        console.log(`🔍 [CACHE MISS DEBUG] - Total searches: ${allSearches.length}`);
        console.log(`🔍 [CACHE MISS DEBUG] - Looking for hash: ${searchHash}`);
        console.log(`🔍 [CACHE MISS DEBUG] - This shows workspace isolation`);
        allSearches.forEach((s, i) => {
            const hashMatch = s.searchHash === searchHash;
            console.log(`🔍 [CACHE MISS DEBUG]   ${i + 1}. ID: ${s.id}`);
            console.log(`🔍 [CACHE MISS DEBUG]       Hash: ${s.searchHash.substring(0, 8)}...${s.searchHash.substring(s.searchHash.length - 8)}`);
            console.log(`🔍 [CACHE MISS DEBUG]       Match: ${hashMatch ? '✅ YES' : '❌ NO'}`);
            console.log(`🔍 [CACHE MISS DEBUG]       Created: ${s.createdAt.toISOString()}`);
            console.log(`🔍 [CACHE MISS DEBUG]       Is Active: ${s.isActive}`);
            console.log(`🔍 [CACHE MISS DEBUG]       Result Count: ${s.resultIds?.length || 0}`);
            console.log(`🔍 [CACHE MISS DEBUG]       ---`);
        });
        console.log(`🔍 [CACHE MISS DEBUG] ==========================================`);
        
        // Try to fix any corrupted searches that might be causing cache issues
        try {
            const fixedCount = await leadSearchService.fixCorruptedSearches(workspaceId);
            if (fixedCount > 0) {
                console.log(`🔧 [FIX DEBUG] Fixed ${fixedCount} corrupted searches, checking cache again...`);
                // Try to find the search again after fixing
                const fixedSearch = await leadSearchService.findByWorkspaceAndHash(workspaceId, searchHash);
                if (fixedSearch && fixedSearch.lastExecutedAt) {
                    console.log(`🔧 [FIX DEBUG] ✅ Found fixed search! Using cache instead of Apollo`);
                    existingSearch = fixedSearch;
                }
            }
        } catch (error) {
            console.error(`🔧 [FIX DEBUG] Error fixing corrupted searches:`, error);
        }
    }
    
    if (!existingSearch) {
        console.log(`🔍 [SEARCH DEBUG] 🚀 CALLING APOLLO API (CACHE MISS)`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        console.log(`🔍 [SEARCH DEBUG] 📡 APOLLO SEARCH PARAMETERS:`);
        console.log(`🔍 [SEARCH DEBUG] - Workspace ID: ${workspaceId}`);
        console.log(`🔍 [SEARCH DEBUG] - Filters: ${JSON.stringify(filters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - Lead Type: ${leadType}`);
        console.log(`🔍 [SEARCH DEBUG] - Note: excludeMyLeads (${excludeMyLeads}) is NOT sent to Apollo`);
        console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads is only used for result filtering`);
        console.log(`🔍 [SEARCH DEBUG] - Apollo will return ALL results regardless of excludeMyLeads`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        // Check if we have an existing search and need to get more results
        let apolloPage = 1; // Default to page 1
        if (existingSearch && existingSearch.metadata?.apolloPage) {
            apolloPage = existingSearch.metadata.apolloPage + 1; // Get next page
            console.log(`🔍 [SEARCH DEBUG] 🔄 Getting next Apollo page: ${apolloPage}`);
            console.log(`🔍 [SEARCH DEBUG] 🛡️ SAFETY NET: If bug causes Apollo call, we get different results`);
            console.log(`🔍 [SEARCH DEBUG] 🛡️ This ensures M1-M100 becomes M101-M200, expanding our database`);
        }
        
        const apolloSearchResults = await executeApolloSearch(workspaceId, filters, leadType, excludeMyLeads, apolloPage);
        console.log(`🔍 [SEARCH DEBUG] 📥 APOLLO API RESPONSE RECEIVED:`);
        console.log(`🔍 [SEARCH DEBUG] - People Count: ${apolloSearchResults.people?.length || 0}`);
        console.log(`🔍 [SEARCH DEBUG] - Organizations Count: ${apolloSearchResults.organizations?.length || 0}`);
        console.log(`🔍 [SEARCH DEBUG] - Total Count: ${apolloSearchResults.totalCount}`);
        console.log(`🔍 [SEARCH DEBUG] - Request ID: ${apolloSearchResults.requestId}`);
        console.log(`🔍 [SEARCH DEBUG] - Credits Used: ${apolloSearchResults.creditsUsed}`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    }
    
    if (existingSearch && existingSearch.lastExecutedAt) {
        console.log(`🔍 [SEARCH DEBUG] 🎯 CACHE HIT! Using existing search results`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        console.log(`🔍 [SEARCH DEBUG] ✅ Found existing search! ID: ${existingSearch.id}`);
        console.log(`🔍 [SEARCH DEBUG] 🛡️ SAFETY NET: Cache hit prevents Apollo calls, saving credits`);
        console.log(`🔍 [SEARCH DEBUG] 🛡️ If bugs occur, next Apollo call gets different results (M101-M200)`);
        console.log(`🔍 [SEARCH DEBUG] 📊 Existing search details:`, {
            totalCount: existingSearch.totalCount,
            numberLoaded: existingSearch.numberLoaded,
            lastExecutedAt: existingSearch.lastExecutedAt,
            resultIdsCount: existingSearch.resultIds?.length || 0,
            apolloPage: existingSearch.metadata?.apolloPage || 1,
            apolloTotalPages: existingSearch.metadata?.apolloTotalPages || 1
        });
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        // Check if we need more results from Apollo (if user requests page beyond what we have)
        const maxResultsPerPage = 50;
        const totalResultsInCache = existingSearch.resultIds?.length || 0;
        const totalPagesAvailable = Math.ceil(totalResultsInCache / maxResultsPerPage);
        const requestedPage = pagination.page;
        
        console.log(`🔍 [CACHE PAGINATION DEBUG] ==========================================`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] 📄 DETAILED PAGINATION ANALYSIS`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Requested Page: ${requestedPage}`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Total Results in Cache: ${totalResultsInCache}`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Results per Page: ${maxResultsPerPage}`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Calculation: Math.ceil(${totalResultsInCache} / ${maxResultsPerPage}) = ${totalPagesAvailable}`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Result IDs Array Length: ${existingSearch.resultIds?.length || 0}`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] - Result IDs Sample: ${existingSearch.resultIds?.slice(0, 3).join(', ') || 'None'}...`);
        console.log(`🔍 [CACHE PAGINATION DEBUG] ==========================================`);
        
        if (requestedPage <= totalPagesAvailable) {
            // We have enough cached results for this page
            console.log(`🔍 [SEARCH DEBUG] ✅ Page ${requestedPage} available in cache`);
            console.log(`🔍 [SEARCH DEBUG] 📊 CACHE STATUS: ${existingSearch.resultIds?.length || 0} total leads cached`);
            
            // Get leads for the requested page from database
            const startIndex = (requestedPage - 1) * maxResultsPerPage;
            const endIndex = startIndex + maxResultsPerPage;
            const pageResultIds = existingSearch.resultIds?.slice(startIndex, endIndex) || [];
            
            console.log(`🔍 [SEARCH DEBUG] 📄 PAGINATION DETAILS:`);
            console.log(`🔍 [SEARCH DEBUG] - Requested Page: ${requestedPage}`);
            console.log(`🔍 [SEARCH DEBUG] - Requested Limit: ${pagination.limit}`);
            console.log(`🔍 [SEARCH DEBUG] - Lead IDs for this page: ${pageResultIds.length} IDs`);
            console.log(`🔍 [SEARCH DEBUG] - Start Index: ${startIndex}, End Index: ${endIndex}`);
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
            
            // Retrieve leads from database for this page
            const leadService = new LeadService();
            const leads = await leadService.findByWorkspaceAndIds(workspaceId, pageResultIds);
            console.log(`🔍 [SEARCH DEBUG] 📥 RETRIEVED LEADS FROM DATABASE:`);
            console.log(`🔍 [SEARCH DEBUG] - Retrieved: ${leads.length} leads`);
            console.log(`🔍 [SEARCH DEBUG] - Workspace ID: ${workspaceId}`);
            console.log(`🔍 [SEARCH DEBUG] - This ensures workspace isolation`);
        if (leads.length > 0) {
                const sampleNames = leads.slice(0, 3).map(lead => lead.name).join(', ');
                console.log(`🔍 [SEARCH DEBUG] - Sample lead names: ${sampleNames}`);
        }
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
            // Apply data privacy and exclude my leads
        const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
        console.log(`🔍 [SEARCH DEBUG] After processing: ${processedLeads.length} leads`);
        if (processedLeads.length > 0) {
                const sampleNames = processedLeads.slice(0, 3).map(lead => lead.name).join(', ');
                console.log(`🔍 [SEARCH DEBUG] - Sample processed lead names: ${sampleNames}`);
        }
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        console.log(`🔍 [SEARCH DEBUG] 🚫 Returning CACHED results from database (not calling Apollo)`);
        console.log(`🔍 [SEARCH DEBUG] 📊 SEARCH SUMMARY:`, {
            decision: 'CACHE_HIT',
            reason: 'Existing search found',
                searchHash: searchHash,
            existingSearchId: existingSearch.id,
            cachedResults: processedLeads.length,
                totalAvailable: existingSearch.totalCount,
                requestedPage: requestedPage,
                totalPagesAvailable: totalPagesAvailable,
                apolloPage: existingSearch.metadata?.apolloPage || 1,
                apolloTotalPages: existingSearch.metadata?.apolloTotalPages || 1
            });
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
            
            console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION (Cache Hit):`);
            console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        return {
            leads: processedLeads,
            totalCount: existingSearch.totalCount,
                hasNextPage: requestedPage < totalPagesAvailable,
            searchId: existingSearch.id,
            filters: existingSearch.filters,
                metadata: {
                    apolloRequestId: existingSearch.metadata?.apolloRequestId,
                    apolloCreditsUsed: 0, // No credits used for cached results
                    processingTimeMs: 0,
                    resultQuality: 'high',
                    dataFreshness: existingSearch.lastExecutedAt || existingSearch.createdAt,
                    apolloPage: existingSearch.metadata?.apolloPage || 1,
                    apolloTotalPages: existingSearch.metadata?.apolloTotalPages || 1,
                    totalPagesAvailable: totalPagesAvailable // Add this to show frontend how many pages are cached
                }
            };
        } else {
            // User requested a page beyond what we have cached
            console.log(`🔍 [SEARCH DEBUG] ⚠️ Page ${requestedPage} requested but only ${totalPagesAvailable} pages available in cache`);
            console.log(`🔍 [SEARCH DEBUG] 🔄 Need to get more results from Apollo`);
            
            // Check if we can get more from Apollo
            const currentApolloPage = existingSearch.metadata?.apolloPage || 1;
            const apolloTotalPages = existingSearch.metadata?.apolloTotalPages || 1;
            
            console.log(`🔍 [SEARCH DEBUG] 📊 APOLLO PAGE STATUS:`);
            console.log(`🔍 [SEARCH DEBUG] - Current Apollo Page: ${currentApolloPage}`);
            console.log(`🔍 [SEARCH DEBUG] - Total Apollo Pages Available: ${apolloTotalPages}`);
            console.log(`🔍 [SEARCH DEBUG] - Can Get More: ${currentApolloPage < apolloTotalPages ? 'YES' : 'NO'}`);
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
            
            if (currentApolloPage < apolloTotalPages) {
                console.log(`🔍 [SEARCH DEBUG] ✅ More Apollo pages available (${currentApolloPage}/${apolloTotalPages})`);
                console.log(`🔍 [SEARCH DEBUG] 🔄 Getting next Apollo page: ${currentApolloPage + 1}`);
                
                // Get next Apollo page
                const nextApolloPage = currentApolloPage + 1;
                const apolloSearchResults = await executeApolloSearch(workspaceId, filters, leadType, excludeMyLeads, nextApolloPage);
                
                // Process and store new leads
                // Fix: Use the correct data based on lead type
                let apolloResults: ApolloPersonData[] | ApolloCompanyData[] = [];
                if (leadType === LeadType.Person) {
                    apolloResults = apolloSearchResults.people || [];
                } else {
                    apolloResults = apolloSearchResults.organizations || [];
                }
                
                console.log(`🔍 [SEARCH DEBUG] 🔧 DATA SELECTION FOR PROCESSING (Pagination):`);
                console.log(`🔍 [SEARCH DEBUG] - Lead Type: ${leadType}`);
                console.log(`🔍 [SEARCH DEBUG] - People Count: ${apolloSearchResults.people?.length || 0}`);
                console.log(`🔍 [SEARCH DEBUG] - Organizations Count: ${apolloSearchResults.organizations?.length || 0}`);
                console.log(`🔍 [SEARCH DEBUG] - Selected for Processing: ${apolloResults.length} ${leadType === LeadType.Person ? 'people' : 'organizations'}`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Results Type: ${typeof apolloResults}`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Results Is Array: ${Array.isArray(apolloResults)}`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Results Length: ${apolloResults.length}`);
                if (apolloResults.length > 0) {
                    console.log(`🔍 [SEARCH DEBUG] - First Result Type: ${typeof apolloResults[0]}`);
                    console.log(`🔍 [SEARCH DEBUG] - First Result Keys: ${Object.keys(apolloResults[0])}`);
                }
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                const newLeads = await processAndStoreLeads({
                    workspaceId, 
                    apolloResults, 
                    searchHash, 
                    userId, 
                    leadType
                });
                
                // Update existing search with new results
                const updatedResultIds = [...(existingSearch.resultIds || []), ...newLeads.map(lead => lead.id)];
                console.log(`🔍 [SEARCH DEBUG] 📊 UPDATING SEARCH WITH NEW RESULTS:`);
                console.log(`🔍 [SEARCH DEBUG] - Previous Total: ${existingSearch.resultIds?.length || 0} leads`);
                console.log(`🔍 [SEARCH DEBUG] - New Results: ${newLeads.length} leads`);
                console.log(`🔍 [SEARCH DEBUG] - Updated Total: ${updatedResultIds.length} leads`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Page Used: ${nextApolloPage}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                await leadSearchService.updateSearchResults(existingSearch.id, {
                    resultIds: updatedResultIds,
                    totalCount: Math.max(existingSearch.totalCount, apolloSearchResults.totalCount || 0),
                    numberLoaded: updatedResultIds.length,
                    pageLoaded: pagination.page,
                    metadata: {
                        ...existingSearch.metadata,
                        apolloRequestId: apolloSearchResults.requestId,
                        apolloCreditsUsed: (existingSearch.metadata?.apolloCreditsUsed || 0) + (apolloSearchResults.creditsUsed || 0),
                        processingTimeMs: (existingSearch.metadata?.processingTimeMs || 0) + (apolloSearchResults.processingTime || 0),
                        apolloPage: nextApolloPage,
                        apolloTotalPages: apolloTotalPages
                    }
                });
                
                // Update the apolloPage field separately
                await leadSearchService.update({ id: existingSearch.id }, { apolloPage: nextApolloPage });
                
                // Now return the requested page
                const startIndex = (requestedPage - 1) * maxResultsPerPage;
                const endIndex = startIndex + maxResultsPerPage;
                const pageResultIds = updatedResultIds.slice(startIndex, endIndex);
                
                const leadService = new LeadService();
                const leads = await leadService.findByWorkspaceAndIds(workspaceId, pageResultIds);
                const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
                
                console.log(`🔍 [SEARCH DEBUG] 🔄 Returning results from Apollo page ${nextApolloPage} for requested page ${requestedPage}`);
                console.log(`🔍 [SEARCH DEBUG] 📊 FINAL RESULT:`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Page Used: ${nextApolloPage}`);
                console.log(`🔍 [SEARCH DEBUG] - Frontend Page Served: ${requestedPage}`);
                console.log(`🔍 [SEARCH DEBUG] - Leads Returned: ${processedLeads.length}`);
                console.log(`🔍 [SEARCH DEBUG] - Total Cached: ${updatedResultIds.length}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                // Calculate total pages available for frontend pagination
                const totalPagesAvailable = Math.ceil(updatedResultIds.length / maxResultsPerPage);
                console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION:`);
                console.log(`🔍 [SEARCH DEBUG] - Total Result IDs: ${updatedResultIds.length}`);
                console.log(`🔍 [SEARCH DEBUG] - Max Results Per Page: ${maxResultsPerPage}`);
                console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                return {
                    leads: processedLeads,
                    totalCount: Math.max(existingSearch.totalCount, apolloSearchResults.totalCount || 0),
                    hasNextPage: requestedPage < totalPagesAvailable,
                    searchId: existingSearch.id,
                    filters: existingSearch.filters,
                    metadata: {
                        apolloRequestId: apolloSearchResults.requestId,
                        apolloCreditsUsed: apolloSearchResults.creditsUsed || 0,
                        processingTimeMs: apolloSearchResults.processingTime || 0,
                        resultQuality: 'high',
                        dataFreshness: new Date(),
                        apolloPage: nextApolloPage,
                        apolloTotalPages: apolloTotalPages,
                        totalPagesAvailable: totalPagesAvailable
                    }
                };
            } else {
                console.log(`🔍 [SEARCH DEBUG] ❌ No more Apollo pages available`);
                console.log(`🔍 [SEARCH DEBUG] 🔄 Returning last available page with warning`);
                
                // Return the last available page
                const lastPage = totalPagesAvailable;
                const startIndex = (lastPage - 1) * maxResultsPerPage;
                const endIndex = startIndex + maxResultsPerPage;
                const pageResultIds = existingSearch.resultIds?.slice(startIndex, endIndex) || [];
                
                const leadService = new LeadService();
                const leads = await leadService.findByWorkspaceAndIds(workspaceId, pageResultIds);
                const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
                
                console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION (Last Page):`);
                console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                return {
                    leads: processedLeads,
                    totalCount: existingSearch.totalCount,
                    hasNextPage: false, // No more pages available
                    searchId: existingSearch.id,
                    filters: existingSearch.filters,
                    metadata: {
                        ...existingSearch.metadata,
                        resultQuality: 'high',
                        dataFreshness: existingSearch.lastExecutedAt || existingSearch.createdAt,
                        totalPagesAvailable: totalPagesAvailable
                    }
                };
            }
        }
    }

    console.log(`🔍 [SEARCH DEBUG] ❌ No existing search found - calling Apollo API`);
    console.log(`🔍 [SEARCH DEBUG] Apollo search parameters:`, {
        workspaceId,
        filters: JSON.stringify(filters),
        leadType,
        excludeMyLeads
    });

    // Get the next Apollo page for this search criteria
    let apolloPage = 1;
    if (existingSearch && existingSearch.apolloPage) {
        apolloPage = existingSearch.apolloPage + 1; // Increment to next page
        console.log(`🔍 [SEARCH DEBUG] 🔄 INCREMENTING APOLLO PAGE: ${existingSearch.apolloPage} → ${apolloPage}`);
    } else {
        console.log(`🔍 [SEARCH DEBUG] 🆕 FIRST APOLLO PAGE: ${apolloPage}`);
    }
    console.log(`🔍 [SEARCH DEBUG] 🚀 CALLING APOLLO WITH PAGE: ${apolloPage}`);
    const apolloSearchResults = await executeApolloSearch(workspaceId, filters, leadType, excludeMyLeads, apolloPage);
    console.log(`🔍 [SEARCH DEBUG] Apollo returned:`, {
        peopleCount: apolloSearchResults.people?.length || 0,
        organizationsCount: apolloSearchResults.organizations?.length || 0,
        totalCount: apolloSearchResults.totalCount,
        requestId: apolloSearchResults.requestId,
        creditsUsed: apolloSearchResults.creditsUsed
    });
    
    // Log sample names from Apollo results to debug data mixing
    if (apolloSearchResults.people && apolloSearchResults.people.length > 0) {
        const sampleApolloNames = apolloSearchResults.people.slice(0, 3).map(p => p.name || p.firstName + ' ' + p.lastName).join(', ');
        console.log(`🔍 [SEARCH DEBUG] Sample Apollo people names: ${sampleApolloNames}`);
    }
    
    // Fix: Use the correct data based on lead type
    let apolloResults: ApolloPersonData[] | ApolloCompanyData[] = [];
    if (leadType === LeadType.Person) {
        apolloResults = apolloSearchResults.people || [];
    } else {
        apolloResults = apolloSearchResults.organizations || [];
    }
    
    console.log(`🔍 [SEARCH DEBUG] 🔧 DATA SELECTION FOR PROCESSING:`);
    console.log(`🔍 [SEARCH DEBUG] - Lead Type: ${leadType}`);
    console.log(`🔍 [SEARCH DEBUG] - People Count: ${apolloSearchResults.people?.length || 0}`);
    console.log(`🔍 [SEARCH DEBUG] - Organizations Count: ${apolloSearchResults.organizations?.length || 0}`);
    console.log(`🔍 [SEARCH DEBUG] - Selected for Processing: ${apolloResults.length} ${leadType === LeadType.Person ? 'people' : 'organizations'}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Results Type: ${typeof apolloResults}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Results Is Array: ${Array.isArray(apolloResults)}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Results Length: ${apolloResults.length}`);
    if (apolloResults.length > 0) {
        console.log(`🔍 [SEARCH DEBUG] - First Result Type: ${typeof apolloResults[0]}`);
        console.log(`🔍 [SEARCH DEBUG] - First Result Keys: ${Object.keys(apolloResults[0])}`);
    }
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    console.log(`🔍 [APOLLO RESULTS DEBUG] ==========================================`);
    console.log(`🔍 [APOLLO RESULTS DEBUG] 📊 APOLLO RESULTS ANALYSIS`);
    console.log(`🔍 [APOLLO RESULTS DEBUG] - Total Apollo results received: ${apolloResults.length}`);
    console.log(`🔍 [APOLLO RESULTS DEBUG] - Apollo total count: ${apolloSearchResults.totalCount}`);
    console.log(`🔍 [APOLLO RESULTS DEBUG] - Apollo page: ${apolloPage}`);
    console.log(`🔍 [APOLLO RESULTS DEBUG] ==========================================`);
    
    const leads = await processAndStoreLeads({workspaceId, apolloResults, searchHash, userId, leadType});
    console.log(`🔍 [SEARCH DEBUG] Processed and stored ${leads.length} leads in database`);
    
    console.log(`🔍 [LEADS PROCESSING DEBUG] ==========================================`);
    console.log(`🔍 [LEADS PROCESSING DEBUG] 📊 LEADS PROCESSING ANALYSIS`);
    console.log(`🔍 [LEADS PROCESSING DEBUG] - Apollo results input: ${apolloResults.length}`);
    console.log(`🔍 [LEADS PROCESSING DEBUG] - Processed leads output: ${leads.length}`);
    console.log(`🔍 [LEADS PROCESSING DEBUG] - Lead IDs to store: ${leads.map(l => l.id).join(', ')}`);
    console.log(`🔍 [LEADS PROCESSING DEBUG] ==========================================`);
    
    // Create or update search record
    if (existingSearch) {
        console.log(`🔍 [SEARCH DEBUG] Updating existing search record: ${existingSearch.id}`);
        
        // Accumulate new results with existing results
        const updatedResultIds = [...(existingSearch.resultIds || []), ...leads.map(lead => lead.id)];
        console.log(`🔍 [SEARCH DEBUG] 📊 ACCUMULATING RESULTS: ${existingSearch.resultIds?.length || 0} existing + ${leads.length} new = ${updatedResultIds.length} total`);
        
        // Update search results
        await leadSearchService.updateSearchResults(existingSearch.id, {
            resultIds: updatedResultIds,
            totalCount: apolloSearchResults.totalCount || updatedResultIds.length,
            numberLoaded: updatedResultIds.length,
            pageLoaded: pagination.page,
            metadata: {
                apolloRequestId: apolloSearchResults.requestId,
                apolloCreditsUsed: apolloSearchResults.creditsUsed,
                processingTimeMs: apolloSearchResults.processingTime,
                resultQuality: 'high',
                dataFreshness: new Date(),
                apolloPage: apolloPage, // Track which Apollo page we're on (incremented)
                apolloTotalPages: Math.ceil((apolloSearchResults.totalCount || 0) / 100) // Calculate total Apollo pages
            }
        });
        
        // Update the apolloPage field and lastExecutedAt separately
        await leadSearchService.update({ id: existingSearch.id }, { 
            apolloPage,
            lastExecutedAt: new Date()
        });
    } else {
        console.log(`🔍 [SEARCH DEBUG] Creating new search record with hash: ${searchHash}`);
        
        // Clean filters before storing - excludeMyLeads is NOT a search parameter
        const cleanFilters = { ...filters };
        delete cleanFilters.excludeMyLeads;
        
        console.log(`🔍 [SEARCH DEBUG] 🔧 FILTERS CLEANING:`);
        console.log(`🔍 [SEARCH DEBUG] - Original filters: ${JSON.stringify(filters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - Cleaned filters: ${JSON.stringify(cleanFilters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads removed from search criteria`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        await leadSearchService.createSearch({
            workspaceId,
            searchHash,
            filters: cleanFilters,  // Store clean filters
            pagination,
            resultIds: leads.map(lead => lead.id),
            totalCount: apolloSearchResults.totalCount || leads.length,
            numberLoaded: leads.length,
            pageLoaded: pagination.page,
            createdById: userId,
            isActive: true,
            apolloPage: apolloPage, // Pass apolloPage to the service
            lastExecutedAt: new Date(), // Set lastExecutedAt for cache hit detection
            metadata: {
                apolloRequestId: apolloSearchResults.requestId,
                apolloCreditsUsed: apolloSearchResults.creditsUsed,
                processingTimeMs: apolloSearchResults.processingTime,
                resultQuality: 'high',
                dataFreshness: new Date(),
                apolloPage: apolloPage, // Track which Apollo page we're on
                apolloTotalPages: Math.ceil((apolloSearchResults.totalCount || 0) / 100), // Calculate total Apollo pages
                totalPagesAvailable: Math.ceil((apolloSearchResults.totalCount || leads.length) / 50) // Add total pages available for this search
            }
        });
        
        console.log(`🔍 [SEARCH DEBUG] ✅ SEARCH RECORD CREATED:`);
        console.log(`🔍 [SEARCH DEBUG] - Search Hash: ${searchHash}`);
        console.log(`🔍 [SEARCH DEBUG] - Cleaned Filters: ${JSON.stringify(cleanFilters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads: NOT stored in search record`);
        console.log(`🔍 [SEARCH DEBUG] - This search can now be cached for future requests`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    }

    const searchRecord = existingSearch || await leadSearchService.findByWorkspaceAndHash(workspaceId, searchHash);

    // Apply data privacy and exclude my leads
    const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
    console.log(`🔍 [SEARCH DEBUG] After privacy processing: ${processedLeads.length} leads`);
    
    // Get the total accumulated results for proper pagination
    const totalAccumulatedResults = existingSearch ? 
        (existingSearch.resultIds?.length || 0) + leads.length : 
        leads.length;
    
    // Apply pagination to respect user's requested limit (max 50 per page)
    const maxLimit = Math.min(pagination.limit, 50); // Force max 50 results per page
    const startIndex = (pagination.page - 1) * maxLimit;
    const endIndex = startIndex + maxLimit;
    const paginatedLeads = processedLeads.slice(startIndex, endIndex);
    
    console.log(`🔍 [SEARCH DEBUG] Final pagination:`, {
        maxLimit,
        startIndex,
        endIndex,
        paginatedLeadsCount: paginatedLeads.length
    });

    console.log(`🔍 [SEARCH DEBUG] 🆕 Returning FRESH results from Apollo API`);
    console.log(`🔍 [SEARCH DEBUG] 📊 SEARCH SUMMARY:`);
    console.log(`🔍 [SEARCH DEBUG] - Decision: APOLLO_CALLED`);
    console.log(`🔍 [SEARCH DEBUG] - Reason: No existing search found`);
    console.log(`🔍 [SEARCH DEBUG] - Search Hash: ${searchHash}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Page Used: ${apolloPage}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Results: ${apolloSearchResults.people?.length || apolloSearchResults.organizations?.length || 0}`);
    console.log(`🔍 [SEARCH DEBUG] - Final Results: ${paginatedLeads.length}`);
    console.log(`🔍 [SEARCH DEBUG] - Total Accumulated: ${totalAccumulatedResults}`);
    console.log(`🔍 [SEARCH DEBUG] - Total Available from Apollo: ${apolloSearchResults.totalCount || leads.length}`);
    console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads Filtering: Applied to Apollo results`);
    console.log(`🔍 [SEARCH DEBUG] - Search Record: Created for future caching`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    // Calculate total pages available for frontend pagination
    const totalPagesAvailable = Math.ceil(totalAccumulatedResults / 50);
    console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION (New Search):`);
    console.log(`🔍 [SEARCH DEBUG] - Total Accumulated Results: ${totalAccumulatedResults}`);
    console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    return {
        leads: paginatedLeads,
        totalCount: apolloSearchResults.totalCount || totalAccumulatedResults,
        hasNextPage: totalAccumulatedResults > endIndex,
        searchId: searchRecord?.id || '',
        filters,
        metadata: {
            ...searchRecord?.metadata,
            totalPagesAvailable: totalPagesAvailable
        }
    };
}


async function executeApolloSearch(workspaceId: string, filters: SearchFilters, leadType: LeadType = LeadType.Person, excludeMyLeads: boolean = false, apolloPage: number = 1) {
    console.log(`🔍 [APOLLO DEBUG] 🚀 STARTING APOLLO SEARCH EXECUTION`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    console.log(`🔍 [APOLLO DEBUG] 📋 SEARCH PARAMETERS:`);
    console.log(`🔍 [APOLLO DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔍 [APOLLO DEBUG] - Lead Type: ${leadType}`);
    console.log(`🔍 [APOLLO DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
    console.log(`🔍 [APOLLO DEBUG] - Filters: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Backend passes filters through as-is - no conversion
    console.log(`🔍 [APOLLO DEBUG] 🔧 FILTER PASSING:`);
    console.log(`🔍 [APOLLO DEBUG] - Original Filters: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] - Passing to Integration: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    let result;
    
    try {
        if (leadType === LeadType.Person) {
            console.log(`🔍 [APOLLO DEBUG] 🚀 CALLING APOLLO INTEGRATION: advancedPeopleSearch`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            console.log(`🔍 [APOLLO DEBUG] 📡 INTEGRATION CALL DETAILS:`);
            console.log(`🔍 [APOLLO DEBUG] - Integration: apollo`);
            console.log(`🔍 [APOLLO DEBUG] - Action: advancedPeopleSearch`);
            console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
            console.log(`🔍 [APOLLO DEBUG] - Search Parameters: ${JSON.stringify(filters)}`);
            console.log(`🔍 [APOLLO DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            
            result = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'advancedPeopleSearch',
                    type: 'action',
                    mode: 'run',
                    propsValue: {
                        searchParams: JSON.stringify(filters),
                        page: apolloPage.toString()
                    }
                },
                'apollo-default'
            );
            
            console.log(`🔍 [APOLLO DEBUG] ✅ Apollo integration call completed successfully`);
            console.log(`🔍 [APOLLO DEBUG] 📥 Raw Response Received:`, JSON.stringify(result, null, 2));
        } else {
            console.log(`🔍 [APOLLO DEBUG] 🚀 CALLING APOLLO INTEGRATION: searchOrganizations`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            console.log(`🔍 [APOLLO DEBUG] 📡 INTEGRATION CALL DETAILS:`);
            console.log(`🔍 [APOLLO DEBUG] - Integration: apollo`);
            console.log(`🔍 [APOLLO DEBUG] - Action: searchOrganizations`);
            console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
            console.log(`🔍 [APOLLO DEBUG] - Search Parameters: ${JSON.stringify(filters)}`);
            console.log(`🔍 [APOLLO DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            
            result = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'searchOrganizations',
                    type: 'action',
                    mode: 'run',
                    propsValue: {
                        searchParams: JSON.stringify(filters),
                        page: apolloPage.toString()
                    }
                },
                'apollo-default'
            );
            
            console.log(`🔍 [APOLLO DEBUG] ✅ Apollo integration call completed successfully`);
            console.log(`🔍 [APOLLO DEBUG] 📥 Raw Response Received:`, JSON.stringify(result, null, 2));
        }
        
        console.log(`🔍 [APOLLO DEBUG] Apollo integration result:`, {
            hasResult: !!result,
            resultKeys: result ? Object.keys(result) : [],
            rawResult: JSON.stringify(result, null, 2)
        });
        
    } catch (error) {
        console.error(`🔍 [APOLLO DEBUG] ERROR calling Apollo API:`, error);
        throw error;
    }

    // Process the final result
    console.log(`🔍 [APOLLO DEBUG] 🔍 PROCESSING FINAL RESULT:`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Handle both nested and direct response structures
    console.log(`🔍 [APOLLO DEBUG] 🔍 RESPONSE STRUCTURE ANALYSIS:`);
    console.log(`🔍 [APOLLO DEBUG] - Has result: ${!!result}`);
    console.log(`🔍 [APOLLO DEBUG] - Has result.result: ${!!result?.result}`);
    console.log(`🔍 [APOLLO DEBUG] - Result keys: ${result ? Object.keys(result) : 'undefined'}`);
    console.log(`🔍 [APOLLO DEBUG] - Result.result keys: ${result?.result ? Object.keys(result.result) : 'undefined'}`);
    console.log(`🔍 [APOLLO DEBUG] - Direct organizations: ${result?.organizations?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Nested organizations: ${result?.result?.organizations?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Handle both nested and direct response structures
    const finalResult = result?.result || result || { people: [], organizations: [], totalCount: 0 };
    
    console.log(`🔍 [APOLLO DEBUG] 📊 FINAL PROCESSED RESULT:`);
    console.log(`🔍 [APOLLO DEBUG] - People Count: ${finalResult.people?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Organizations Count: ${finalResult.organizations?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Total Count: ${finalResult.totalCount}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Page Requested: ${apolloPage}`);
    console.log(`🔍 [APOLLO DEBUG] - Request ID: ${finalResult.requestId || finalResult.request_id || 'N/A'}`);
    console.log(`🔍 [APOLLO DEBUG] - Credits Used: ${finalResult.creditsUsed || finalResult.credits_used || 'N/A'}`);
    
    // Log sample results for verification
    if (finalResult.people && finalResult.people.length > 0) {
        const samplePeople = finalResult.people.slice(0, 3).map(p => ({ name: p.name, company: p.company }));
        console.log(`🔍 [APOLLO DEBUG] 👥 SAMPLE PEOPLE RESULTS:`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 1: ${JSON.stringify(samplePeople[0])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 2: ${JSON.stringify(samplePeople[1])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 3: ${JSON.stringify(samplePeople[2])}`);
    }
    
    if (finalResult.organizations && finalResult.organizations.length > 0) {
        const sampleOrgs = finalResult.organizations.slice(0, 3).map(o => ({ name: o.name, industry: o.industry }));
        console.log(`🔍 [APOLLO DEBUG] 🏢 SAMPLE ORGANIZATION RESULTS:`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 1: ${JSON.stringify(sampleOrgs[0])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 2: ${JSON.stringify(sampleOrgs[1])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 3: ${JSON.stringify(sampleOrgs[2])}`);
    }
    
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    console.log(`🔍 [APOLLO DEBUG] ✅ APOLLO SEARCH EXECUTION COMPLETED SUCCESSFULLY`);
    console.log(`🔍 [APOLLO DEBUG] 🚀 RETURNING RESULT:`);
    console.log(`🔍 [APOLLO DEBUG] - Total People: ${finalResult.people?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Total Organizations: ${finalResult.organizations?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Return the processed result with proper structure
    return {
        people: finalResult.people || [],
        organizations: finalResult.organizations || [],
        totalCount: finalResult.totalCount || 0,
        requestId: finalResult.requestId || finalResult.request_id || 'N/A',
        creditsUsed: finalResult.creditsUsed || finalResult.credits_used || 0,
        processingTime: finalResult.processingTime || 0
    };
}



// Filter conversion logic moved to integrations - backend now just passes through

interface ProcessAndStoreLeadsData {
    workspaceId: string;
    apolloResults: ApolloPersonData[] | ApolloCompanyData[];
    searchHash: string;
    userId: string;
    leadType: LeadType;
}


async function processAndStoreLeads( data: ProcessAndStoreLeadsData): Promise<Lead[]> {
    console.log(`🔍 [STORAGE DEBUG] 🚀 STARTING LEAD STORAGE PROCESS`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    console.log(`🔍 [STORAGE DEBUG] 📋 INPUT DATA:`);
    console.log(`🔍 [STORAGE DEBUG] - Workspace ID: ${data.workspaceId}`);
    console.log(`🔍 [STORAGE DEBUG] - Search Hash: ${data.searchHash}`);
    console.log(`🔍 [STORAGE DEBUG] - User ID: ${data.userId}`);
    console.log(`🔍 [STORAGE DEBUG] - Lead Type: ${data.leadType}`);
    console.log(`🔍 [STORAGE DEBUG] - Apollo Results Count: ${data.apolloResults.length}`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    
    // Workspace ID validation removed
    if (!data.searchHash || typeof data.searchHash !== 'string' || data.searchHash.trim().length === 0) {
        throw new RequiredParameterError('Search hash is required');
    }
    if (!data.userId || typeof data.userId !== 'string' || data.userId.trim().length === 0) {
        throw new RequiredParameterError('User ID is required');
    }
    
    const leadService = new LeadService();
    const processedLeads: Lead[] = [];
    let newLeadsCount = 0;
    let existingLeadsCount = 0;

    console.log(`🔍 [STORAGE DEBUG] 🔄 PROCESSING ${data.apolloResults.length} APOLLO RESULTS`);
    
    // Log sample Apollo results to see what we're getting
    if (data.apolloResults.length > 0) {
        const sampleApollo = data.apolloResults.slice(0, 3);
        console.log(`🔍 [STORAGE DEBUG] 👥 SAMPLE APOLLO RESULTS:`);
        console.log(`🔍 [STORAGE DEBUG] - Sample 1: ${JSON.stringify(sampleApollo[0])}`);
        console.log(`🔍 [STORAGE DEBUG] - Sample 2: ${JSON.stringify(sampleApollo[1])}`);
        console.log(`🔍 [STORAGE DEBUG] - Sample 3: ${JSON.stringify(sampleApollo[2])}`);
        console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    }

    for (const apolloData of data.apolloResults) {
        if (!apolloData.id) {
            console.log(`🔍 [STORAGE DEBUG] Skipping result without ID`);
            continue;
        }

        // Apollo data validation removed - trust Apollo's data quality

        let existingLead = await leadService.findByWorkspaceAndApolloId(data.workspaceId, apolloData.id);

        if (existingLead) {
            console.log(`🔍 [STORAGE DEBUG] Found existing lead: ${existingLead.name} (ID: ${existingLead.id})`);
            console.log(`🔍 [STORAGE DEBUG] Existing lead details:`, {
                name: existingLead.name,
                apolloId: existingLead.apolloId,
                searchHashes: existingLead.searchHashes,
                createdAt: existingLead.createdAt
            });
            // Update search hashes
            const searchHashes = existingLead.searchHashes || [];
            if (!searchHashes.includes(data.searchHash)) {
                console.log(`🔍 [STORAGE DEBUG] Adding new search hash to existing lead`);
                searchHashes.push(data.searchHash);
                await leadService.updateSearchHashes(existingLead.id, searchHashes);
            } else {
                console.log(`🔍 [STORAGE DEBUG] Search hash already exists for this lead`);
            }
            processedLeads.push(existingLead);
            existingLeadsCount++;
        } else {
            console.log(`🔍 [STORAGE DEBUG] Creating new lead for Apollo ID: ${apolloData.id}`);
            let normalizedData: NormalizedLeadData;
            if (data.leadType === LeadType.Person) {
                normalizedData = normalizeApolloPersonData(apolloData as ApolloPersonData);
            } else {
                normalizedData = normalizeApolloCompanyData(apolloData as ApolloCompanyData);
            }

            // Normalized data validation removed - trust Apollo's data quality

            const newLead = await leadService.createLead({
                workspaceId: data.workspaceId,
                apolloId: apolloData.id,
                type: data.leadType,
                source: LeadSource.Apollo,
                apolloData,
                normalizedData,
                searchHashes: [data.searchHash],
                email: normalizedData.email,
                name: normalizedData.name,
                companyDomain: normalizedData.companyDomain,
                createdById: data.userId
            });
            
            console.log(`🔍 [STORAGE DEBUG] Created new lead: ${newLead.name} (ID: ${newLead.id})`);
            processedLeads.push(newLead);
            newLeadsCount++;
        }
    }

    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    console.log(`🔍 [STORAGE DEBUG] 📊 STORAGE SUMMARY:`);
    console.log(`🔍 [STORAGE DEBUG] - Total Processed: ${processedLeads.length} leads`);
    console.log(`🔍 [STORAGE DEBUG] - New Leads Created: ${newLeadsCount} leads`);
    console.log(`🔍 [STORAGE DEBUG] - Existing Leads Updated: ${existingLeadsCount} leads`);
    console.log(`🔍 [STORAGE DEBUG] - Search Hash: ${data.searchHash}`);
    console.log(`🔍 [STORAGE DEBUG] - Workspace ID: ${data.workspaceId}`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    
    console.log(`🔍 [STORAGE DEBUG] ✅ LEAD STORAGE PROCESS COMPLETED SUCCESSFULLY`);
    console.log(`🔍 [STORAGE DEBUG] 🚀 RETURNING ${processedLeads.length} PROCESSED LEADS`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);

    return processedLeads;
}

async function processLeadsForResponse(leads: Lead[], excludeMyLeads: boolean, userId: string,  workspaceId: string): Promise<Lead[]> {
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    console.log(`🔒 [FILTER DEBUG] 🚀 STARTING LEAD FILTERING PROCESS`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    console.log(`🔒 [FILTER DEBUG] Input Parameters:`);
    console.log(`🔒 [FILTER DEBUG] - Total leads to process: ${leads.length}`);
    console.log(`🔒 [FILTER DEBUG] - excludeMyLeads: ${excludeMyLeads}`);
    console.log(`🔒 [FILTER DEBUG] - User ID: ${userId}`);
    console.log(`🔒 [FILTER DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    const leadUnlockService = new LeadUnlockService();
    
    if (!excludeMyLeads) {
        console.log(`🔒 [FILTER DEBUG] ✅ excludeMyLeads is FALSE - showing ALL leads`);
        console.log(`🔒 [FILTER DEBUG] - No filtering applied`);
        console.log(`🔒 [FILTER DEBUG] - All ${leads.length} leads will be returned`);
        console.log(`🔒 [FILTER DEBUG] ==========================================`);
        
        // If not excluding, return all leads with privacy protection
        return leads.map(lead => ({
            ...lead,
            normalizedData: {
                ...lead.normalizedData,
                email: lead.isUnlocked ? lead.normalizedData.email : '<EMAIL>',
                phone: lead.isUnlocked ? lead.normalizedData.phone : '<EMAIL>'
            }
        }));
    }
    
    console.log(`🔒 [FILTER DEBUG] 🚫 excludeMyLeads is TRUE - filtering out owned leads`);
    console.log(`🔒 [FILTER DEBUG] - Will exclude leads owned by user ${userId} in workspace ${workspaceId}`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    // If excluding my leads, get unlocked leads and filter them out
    const unlockedLeadIds = await leadUnlockService.getUnlockedLeadsForUser(workspaceId, userId);
    console.log(`🔒 [FILTER DEBUG] 📊 UNLOCKED LEADS LOOKUP:`);
    console.log(`🔒 [FILTER DEBUG] - Found ${unlockedLeadIds.length} leads owned by user`);
    console.log(`🔒 [FILTER DEBUG] - These leads will be EXCLUDED from results`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    const filteredLeads = leads.filter(lead => !unlockedLeadIds.includes(lead.id));
    console.log(`🔒 [FILTER DEBUG] ✅ FILTERING COMPLETED:`);
    console.log(`🔒 [FILTER DEBUG] - Before filtering: ${leads.length} leads`);
    console.log(`🔒 [FILTER DEBUG] - After filtering: ${filteredLeads.length} leads`);
    console.log(`🔒 [FILTER DEBUG] - Filtered out: ${leads.length - filteredLeads.length} leads`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    return filteredLeads
        .map(lead => ({
            ...lead,
            normalizedData: {
                ...lead.normalizedData,
                email: lead.isUnlocked ? lead.normalizedData.email : '<EMAIL>',
                phone: lead.isUnlocked ? lead.normalizedData.phone : '<EMAIL>'
            }
        }));
}

export const GetMyLeads = async ( userId: string, workspaceId: string, options: LeadFilterOptions) => {
    return await GetMyLeadsByType(userId, workspaceId, options, LeadType.Person);
};

export const GetMyCompanyLeads = async (
    userId: string,
    workspaceId: string,
    options: LeadFilterOptions
) => {
    return await GetMyLeadsByType(userId, workspaceId, options, LeadType.Company);
};



async function GetMyLeadsByType(   userId: string,workspaceId: string,options: LeadFilterOptions, leadType: LeadType) {
    console.log('🔍 [BACKEND MY-LEADS] ==========================================');
    console.log('🔍 [BACKEND MY-LEADS] 🚀 STARTING MY-LEADS REQUEST');
    console.log('🔍 [BACKEND MY-LEADS] ==========================================');
    console.log('🔍 [BACKEND MY-LEADS] User ID:', userId);
    console.log('🔍 [BACKEND MY-LEADS] Workspace ID:', workspaceId);
    console.log('🔍 [BACKEND MY-LEADS] Lead Type:', leadType);
    console.log('🔍 [BACKEND MY-LEADS] Options:', JSON.stringify(options, null, 2));
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        console.log('🔍 [BACKEND MY-LEADS] ❌ User not found in workspace');
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        console.log('🔍 [BACKEND MY-LEADS] ❌ User lacks workspace read permission');
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { page, limit, search, filters } = options;
    console.log('🔍 [BACKEND MY-LEADS] 📊 Pagination:', { page, limit });
    console.log('🔍 [BACKEND MY-LEADS] 🔍 Search:', search);
    console.log('🔍 [BACKEND MY-LEADS] 🎯 Filters:', filters);
    
    const leadService = new LeadService();
    const leadUnlockService = new LeadUnlockService();

    console.log('🔍 [BACKEND MY-LEADS] 📋 Getting unlocked lead IDs for user...');
    const unlockedLeadIds = await leadUnlockService.getUnlockedLeadsForUser(workspaceId, userId);
    console.log('🔍 [BACKEND MY-LEADS] 📋 Unlocked lead IDs count:', unlockedLeadIds.length);
    console.log('🔍 [BACKEND MY-LEADS] 📋 Unlocked lead IDs:', unlockedLeadIds);

    if (unlockedLeadIds.length === 0) {
        console.log('🔍 [BACKEND MY-LEADS] ❌ No unlocked leads found for user');
        return {
            leads: [],
            totalCount: 0,
            hasNextPage: false
        };
    }

    console.log('🔍 [BACKEND MY-LEADS] 🔍 Finding leads by workspace and type...');
    const unlockedLeads = await leadService.findByWorkspaceAndType(workspaceId, leadType);
    console.log('🔍 [BACKEND MY-LEADS] 🔍 Total leads in workspace:', unlockedLeads.length);
    console.log('🔍 [BACKEND MY-LEADS] 🔍 Lead IDs in workspace:', unlockedLeads.map(l => l.id));
    
    const filteredLeads = unlockedLeads.filter(lead => unlockedLeadIds.includes(lead.id));
    console.log('🔍 [BACKEND MY-LEADS] 🔍 Filtered leads count:', filteredLeads.length);
    console.log('🔍 [BACKEND MY-LEADS] 🔍 Filtered lead IDs:', filteredLeads.map(l => l.id));

    let finalLeads = filteredLeads;
    if (search) {
        console.log('🔍 [BACKEND MY-LEADS] 🔍 Applying search filter:', search);
        finalLeads = filteredLeads.filter(lead => 
            lead.name?.toLowerCase().includes(search.toLowerCase()) ||
            lead.normalizedData.email?.toLowerCase().includes(search.toLowerCase()) ||
            lead.normalizedData.company?.toLowerCase().includes(search.toLowerCase())
        );
        console.log('🔍 [BACKEND MY-LEADS] 🔍 After search filter count:', finalLeads.length);
    }

    const offset = (page - 1) * limit;
    const paginatedLeads = finalLeads.slice(offset, offset + limit);
    console.log('🔍 [BACKEND MY-LEADS] 📊 Pagination results:', {
        offset,
        limit,
        paginatedCount: paginatedLeads.length,
        totalCount: finalLeads.length,
        hasNextPage: (page * limit) < finalLeads.length
    });

    console.log('🔍 [BACKEND MY-LEADS] ✅ MY-LEADS REQUEST COMPLETED');
    console.log('🔍 [BACKEND MY-LEADS] ==========================================');

    return {
        leads: paginatedLeads,
        totalCount: finalLeads.length,
        hasNextPage: (page * limit) < finalLeads.length
    };
}

export const UnlockLead = async (userId: string,workspaceId: string,leadId: string,request: UnlockLeadRequest) => {
    // All validation removed - trust user input and let business logic handle validation
    
    console.log('🔍 [BACKEND UNLOCK] 🔓 Starting unlock process for lead:', leadId);
    console.log('🔍 [BACKEND UNLOCK] Unlock type requested:', request.unlockType);
    console.log('🔍 [BACKEND UNLOCK] Unlock source:', request.source);
    console.log('🔍 [BACKEND UNLOCK] Will reveal email:', request.unlockType === UnlockType.Email || request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full);
    console.log('🔍 [BACKEND UNLOCK] Will reveal phone:', request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full);
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const leadService = new LeadService();
    const leadUnlockService = new LeadUnlockService();

    const lead = await leadService.findOne({ id: leadId, workspaceId });
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    console.log('🔍 [BACKEND UNLOCK] Lead type:', lead.type);
    console.log('🔍 [BACKEND UNLOCK] Apollo ID:', lead.apolloId);

    const existingUnlock = await leadUnlockService.findExistingUnlock(workspaceId, leadId, userId);
    if (existingUnlock) {
        console.log('🔍 [BACKEND UNLOCK] ℹ️ Lead already unlocked by this user');
        console.log('🔍 [BACKEND UNLOCK] - Unlock ID:', existingUnlock.id);
        console.log('🔍 [BACKEND UNLOCK] - Existing Unlock Type:', existingUnlock.unlockType);
        console.log('🔍 [BACKEND UNLOCK] - Requested Unlock Type:', request.unlockType);
        console.log('🔍 [BACKEND UNLOCK] - Unlocked At:', existingUnlock.unlockedAt);
        
        // 🔍 CHECK: Is this the same unlock type being requested?
        const isSameUnlockType = existingUnlock.unlockType === request.unlockType;
        console.log('🔍 [BACKEND UNLOCK] - Same unlock type requested:', isSameUnlockType);
        
        if (isSameUnlockType) {
            console.log('🔍 [BACKEND UNLOCK] ✅ Same unlock type - checking if data is actually unlocked...');
            
            // Log specific email and phone values for already unlocked leads
            console.log('🔍 [BACKEND UNLOCK] 📧 EMAIL & PHONE STATUS (Already Unlocked):');
            console.log('🔍 [BACKEND UNLOCK] - Lead Type:', lead.type);
            console.log('🔍 [BACKEND UNLOCK] - Apollo Data Keys:', Object.keys(lead.apolloData || {}));
            
            if (lead.apolloData?.person) {
                const personData = lead.apolloData.person as any;
                console.log('🔍 [BACKEND UNLOCK] - Email:', personData.email);
                console.log('🔍 [BACKEND UNLOCK] - Phone:', personData.phone);
                console.log('🔍 [BACKEND UNLOCK] - Email Status:', personData.email_status);
                console.log('🔍 [BACKEND UNLOCK] - Is Email Real:', personData.email !== '<EMAIL>');
                console.log('🔍 [BACKEND UNLOCK] - Is Phone Real:', personData.phone !== '<EMAIL>');
            } else if (lead.apolloData?.organization) {
                const orgData = lead.apolloData.organization as any;
                console.log('🔍 [BACKEND UNLOCK] - Company Phone:', orgData.phone);
                console.log('🔍 [BACKEND UNLOCK] - Primary Phone:', orgData.primary_phone);
            } else {
                console.log('🔍 [BACKEND UNLOCK] - No Apollo data structure found');
                console.log('🔍 [BACKEND UNLOCK] - Raw Apollo Data:', JSON.stringify(lead.apolloData, null, 2));
            }
            
            // Also log normalized data
            console.log('🔍 [BACKEND UNLOCK] 📊 NORMALIZED DATA STATUS:');
            console.log('🔍 [BACKEND UNLOCK] - Normalized Email:', lead.normalizedData?.email);
            console.log('🔍 [BACKEND UNLOCK] - Normalized Phone:', lead.normalizedData?.phone);
            console.log('🔍 [BACKEND UNLOCK] - Is Email Visible:', lead.normalizedData?.isEmailVisible);
            console.log('🔍 [BACKEND UNLOCK] - Is Phone Visible:', lead.normalizedData?.isPhoneVisible);
            
            // 🔍 CHECK: Is the data actually unlocked or just placeholder?
            const hasRealEmail = lead.normalizedData?.email && 
                                lead.normalizedData.email !== '<EMAIL>';
            const hasRealPhone = lead.normalizedData?.phone && 
                                lead.normalizedData.phone !== '<EMAIL>';
            
            console.log('🔍 [BACKEND UNLOCK] - Has Real Email:', hasRealEmail);
            console.log('🔍 [BACKEND UNLOCK] - Has Real Phone:', hasRealPhone);
            
            // If data is actually unlocked, return success
            if ((request.unlockType === UnlockType.Email && hasRealEmail) ||
                (request.unlockType === UnlockType.Phone && hasRealPhone) ||
                (request.unlockType === UnlockType.Full && (hasRealEmail || hasRealPhone))) {
                console.log('🔍 [BACKEND UNLOCK] ✅ Data is actually unlocked - returning success');
        return {
            lead,
            unlock: existingUnlock,
            alreadyUnlocked: true
        };
            } else {
                console.log('🔍 [BACKEND UNLOCK] ⚠️ Data is NOT actually unlocked - will retry integration call');
                console.log('🔍 [BACKEND UNLOCK] - This can happen if previous unlock failed or returned placeholder data');
            }
        } else {
            console.log('🔍 [BACKEND UNLOCK] 🔄 Different unlock type requested - will call integration again');
            console.log('🔍 [BACKEND UNLOCK] - Previous unlock was:', existingUnlock.unlockType);
            console.log('🔍 [BACKEND UNLOCK] - New unlock requested:', request.unlockType);
        }
    }

    let enrichedData = null;
    if (lead.apolloId) {
        try {
            // Use the enhanced Apollo enrichment actions from the integration folder
            // Determine lead type - check if it has person-specific data
            const isPerson = lead.type === LeadType.Person || 
                           (lead.apolloData && 
                            ((lead.apolloData as any).person || 
                             (lead.apolloData as any).first_name || 
                             (lead.apolloData as any).last_name || 
                             (lead.apolloData as any).employment_history));
            
            console.log('🔍 [BACKEND UNLOCK] 🔍 FIRST LEAD TYPE DETECTION:');
            console.log('🔍 [BACKEND UNLOCK] - Lead Type:', lead.type);
            console.log('🔍 [BACKEND UNLOCK] - Has Apollo Data:', !!lead.apolloData);
            console.log('🔍 [BACKEND UNLOCK] - Apollo Data Keys:', lead.apolloData ? Object.keys(lead.apolloData) : 'No data');
            console.log('🔍 [BACKEND UNLOCK] - Has Person Object:', !!(lead.apolloData as any)?.person);
            console.log('🔍 [BACKEND UNLOCK] - Has First Name:', !!(lead.apolloData as any)?.first_name);
            console.log('🔍 [BACKEND UNLOCK] - Has Last Name:', !!(lead.apolloData as any)?.last_name);
            console.log('🔍 [BACKEND UNLOCK] - Has Employment History:', !!(lead.apolloData as any)?.employment_history);
            console.log('🔍 [BACKEND UNLOCK] - Detected as Person:', isPerson);
            
            if (isPerson) {
                // For people: use enhanced peopleEnrichment action
                console.log('🔍 [BACKEND UNLOCK] 🔓 Calling Apollo peopleEnrichment action for person:', lead.apolloId);
                console.log('🔍 [BACKEND UNLOCK] Unlock type requested:', request.unlockType);
                console.log('🔍 [BACKEND UNLOCK] ✅ Lead identified as PERSON - will use peopleEnrichment');
                
                // Prepare props based on unlock type
                const propsValue: any = {
                    personId: lead.apolloId
                };
                
                // For email unlocks: explicitly request email reveal
                if (request.unlockType === UnlockType.Email || request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full) {
                    propsValue.revealEmails = true;
                    console.log('🔍 [BACKEND UNLOCK] Email unlock requested - will reveal personal emails');
                }
                
                // For phone unlocks: add phone reveal and webhook URL
                if (request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full) {
                    propsValue.revealPhone = true;
                    
                    // 🔍 SMART WEBHOOK URL: Handle localhost vs production
                    let webhookUrl: string;
                    console.log('🔍 [DEBUG] isLocal() result:', isLocal());
                    if (isLocal()) {
                        // 🔍 LOCAL DEVELOPMENT: Use ngrok HTTPS URL for testing
                        webhookUrl = 'https://f957cf63733f.ngrok-free.app/webhook/v1/apollo-phone-reveal';
                        console.log('🔍 [BACKEND UNLOCK] 📱 PHONE UNLOCK LOCAL TESTING:');
                        console.log('🔍 [BACKEND UNLOCK] - Webhook URL:', webhookUrl);
                        console.log('🔍 [BACKEND UNLOCK] - Apollo will send phone data to ngrok HTTPS URL');
                        console.log('🔍 [BACKEND UNLOCK] - This will test the complete phone unlock flow');
                    } else {
                        // 🔍 PRODUCTION: Use our own webhook endpoint
                        webhookUrl = `${apiUrl()}/webhook/v1/apollo-phone-reveal`;
                        console.log('🔍 [BACKEND UNLOCK] 📱 PHONE UNLOCK PRODUCTION:');
                        console.log('🔍 [BACKEND UNLOCK] - Webhook URL:', webhookUrl);
                        console.log('🔍 [BACKEND UNLOCK] - Apollo will send phone data to OUR webhook endpoint');
                        console.log('🔍 [BACKEND UNLOCK] - This will complete the phone unlock flow');
                    }
                    propsValue.webhookUrl = webhookUrl;
                    console.log('🔍 [BACKEND UNLOCK] ==========================================');
                }
                
                // 🔍 COMPREHENSIVE LOGGING: Track exactly what we're sending to integration
                console.log('🔍 [BACKEND UNLOCK] 📤 PROPS VALUE BEING SENT TO INTEGRATION:');
                console.log('🔍 [BACKEND UNLOCK] - Raw propsValue object:', JSON.stringify(propsValue, null, 2));
                console.log('🔍 [BACKEND UNLOCK] - personId type:', typeof propsValue.personId);
                console.log('🔍 [BACKEND UNLOCK] - personId value:', JSON.stringify(propsValue.personId));
                console.log('🔍 [BACKEND UNLOCK] - revealEmails type:', typeof propsValue.revealEmails);
                console.log('🔍 [BACKEND UNLOCK] - revealEmails value:', propsValue.revealEmails);
                console.log('🔍 [BACKEND UNLOCK] - revealPhone type:', typeof propsValue.revealPhone);
                console.log('🔍 [BACKEND UNLOCK] - revealPhone value:', propsValue.revealPhone);
                console.log('🔍 [BACKEND UNLOCK] - webhookUrl type:', typeof propsValue.webhookUrl);
                console.log('🔍 [BACKEND UNLOCK] - webhookUrl value:', propsValue.webhookUrl);
                console.log('🔍 [BACKEND UNLOCK] - Total propsValue keys:', Object.keys(propsValue));
                console.log('🔍 [BACKEND UNLOCK] ==========================================');
                
                // 🔍 LOGGING: Track the integration call parameters
                console.log('🔍 [BACKEND UNLOCK] 🚀 CALLING INTEGRATION ACTION:');
                console.log('🔍 [BACKEND UNLOCK] - Workspace ID:', workspaceId);
                console.log('🔍 [BACKEND UNLOCK] - Integration:', 'apollo');
                console.log('🔍 [BACKEND UNLOCK] - Action Name:', 'peopleEnrichment');
                console.log('🔍 [BACKEND UNLOCK] - Action Type:', 'action');
                console.log('🔍 [BACKEND UNLOCK] - Action Mode:', 'run');
                console.log('🔍 [BACKEND UNLOCK] - Auth Profile:', 'apollo-default');
                console.log('🔍 [BACKEND UNLOCK] ==========================================');

            const apolloResult = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                        name: 'peopleEnrichment',
                    type: 'action',
                    mode: 'run',
                    propsValue
                },
                'apollo-default'
            );

                // 🔍 LOGGING: Track the integration response
                console.log('🔍 [BACKEND UNLOCK] 📥 INTEGRATION RESPONSE RECEIVED:');
                console.log('🔍 [BACKEND UNLOCK] - Full apolloResult:', JSON.stringify(apolloResult, null, 2));
                console.log('🔍 [BACKEND UNLOCK] - Has result:', !!apolloResult?.result);
                console.log('🔍 [BACKEND UNLOCK] - Result type:', typeof apolloResult?.result);
                console.log('🔍 [BACKEND UNLOCK] - Result keys:', apolloResult?.result ? Object.keys(apolloResult.result) : 'No result');
                console.log('🔍 [BACKEND UNLOCK] ==========================================');

            if (apolloResult && apolloResult.result) {
                enrichedData = apolloResult.result;
                    console.log('🔍 [BACKEND UNLOCK] ✅ Apollo peopleEnrichment successful for person:', enrichedData);
                    
                    // Log the contact data we got back
                    if (enrichedData.person) {
                        const personData = enrichedData.person as any;
                        console.log('🔍 [BACKEND UNLOCK] 📧 CONTACT DATA RECEIVED:');
                        console.log('🔍 [BACKEND UNLOCK] - Email:', personData.email);
                        console.log('🔍 [BACKEND UNLOCK] - Phone:', personData.phone);
                        console.log('🔍 [BACKEND UNLOCK] - Email Status:', personData.email_status);
                        console.log('🔍 [BACKEND UNLOCK] - Is Email Real:', personData.email !== '<EMAIL>');
                        console.log('🔍 [BACKEND UNLOCK] - Is Phone Real:', personData.phone !== '<EMAIL>');
                    }
            } else {
                    console.warn('🔍 [BACKEND UNLOCK] ⚠️ Apollo peopleEnrichment returned no result for person');
                    console.log('🔍 [BACKEND UNLOCK] ❌ INTEGRATION FAILED - NO RESULT:');
                    console.log('🔍 [BACKEND UNLOCK] - apolloResult:', apolloResult);
                    console.log('🔍 [BACKEND UNLOCK] - apolloResult type:', typeof apolloResult);
                    console.log('🔍 [BACKEND UNLOCK] - apolloResult keys:', apolloResult ? Object.keys(apolloResult) : 'null');
                    console.log('🔍 [BACKEND UNLOCK] ==========================================');
                }
            } else {
                // For companies: use enhanced organizationEnrichment action
                console.log('🔍 [BACKEND UNLOCK] 🔓 Calling Apollo organizationEnrichment action for organization:', lead.apolloId);
                console.log('🔍 [BACKEND UNLOCK] Unlock type requested:', request.unlockType);
                console.log('🔍 [BACKEND UNLOCK] ✅ Lead identified as COMPANY - will use organizationEnrichment');
                
                // Get company domain from the lead data - use the actual domain, not company name
                const companyDomain = lead.normalizedData?.companyDomain || lead.companyDomain;
                console.log('🔍 [BACKEND UNLOCK] Using company domain for enrichment:', companyDomain);
                
                // For companies: just pass domain - no webhook or reveal parameters needed
                // Apollo returns company data immediately (phone, revenue, employees, etc.)
                const propsValue: any = {
                    domain: companyDomain || 'example.com'
                };
                
                console.log('🔍 [BACKEND UNLOCK] Company enrichment - no webhook needed, data returned immediately');
                console.log('🔍 [BACKEND UNLOCK] Company domain for enrichment:', companyDomain);
                
                const apolloResult = await ExecuteWorkspaceIntegrationActionNoAuth(
                    workspaceId,
                    'apollo',
                    {
                        name: 'organizationEnrichment',
                        type: 'action',
                        mode: 'run',
                        propsValue
                    },
                    'apollo-default'
                );

                if (apolloResult && apolloResult.result) {
                    enrichedData = apolloResult.result;
                    console.log('🔍 [BACKEND UNLOCK] ✅ Apollo organizationEnrichment successful for organization:', enrichedData);
                    
                    // Log the contact data we got back
                    if (enrichedData.organization) {
                        const orgData = enrichedData.organization as any;
                        console.log('🔍 [BACKEND UNLOCK] 🏢 CONTACT DATA RECEIVED:');
                        console.log('🔍 [BACKEND UNLOCK] - Company Phone:', orgData.phone);
                        console.log('🔍 [BACKEND UNLOCK] - Primary Phone:', orgData.primary_phone);
                        console.log('🔍 [BACKEND UNLOCK] - Website:', orgData.website_url);
                        console.log('🔍 [BACKEND UNLOCK] - Industry:', orgData.industry);
                    }
                } else {
                    console.warn('🔍 [BACKEND UNLOCK] ⚠️ Apollo organizationEnrichment returned no result for organization');
                }
            }
        } catch (error) {
            console.error('🔍 [BACKEND UNLOCK] ❌ FAILED TO UNLOCK LEAD FROM APOLLO:');
            console.error('🔍 [BACKEND UNLOCK] - Error type:', typeof error);
            console.error('🔍 [BACKEND UNLOCK] - Error message:', error?.message || 'No message');
            console.error('🔍 [BACKEND UNLOCK] - Error stack:', error?.stack || 'No stack');
            console.error('🔍 [BACKEND UNLOCK] - Full error object:', JSON.stringify(error, null, 2));
            console.error('🔍 [BACKEND UNLOCK] ==========================================');
            // Continue without enrichment - this is not a critical failure
        }
    }

    // Determine if unlock is successful based on enrichment result
    const isEnrichmentSuccessful = !!enrichedData;
    
    // For companies: no webhook needed, data returned immediately
    // For people: webhook needed only for phone reveals
    const isPerson = lead.type === LeadType.Person || 
                    (lead.apolloData && 
                     ((lead.apolloData as any).person || 
                      (lead.apolloData as any).first_name || 
                      (lead.apolloData as any).last_name || 
                      (lead.apolloData as any).employment_history));
    
    console.log('🔍 [BACKEND UNLOCK] 🔍 LEAD TYPE DETECTION:');
    console.log('🔍 [BACKEND UNLOCK] - Lead Type:', lead.type);
    console.log('🔍 [BACKEND UNLOCK] - Has Apollo Data:', !!lead.apolloData);
    console.log('🔍 [BACKEND UNLOCK] - Apollo Data Keys:', lead.apolloData ? Object.keys(lead.apolloData) : 'No data');
    console.log('🔍 [BACKEND UNLOCK] - Has Person Object:', !!(lead.apolloData as any)?.person);
    console.log('🔍 [BACKEND UNLOCK] - Has First Name:', !!(lead.apolloData as any)?.first_name);
    console.log('🔍 [BACKEND UNLOCK] - Has Last Name:', !!(lead.apolloData as any)?.last_name);
    console.log('🔍 [BACKEND UNLOCK] - Has Employment History:', !!(lead.apolloData as any)?.employment_history);
    console.log('🔍 [BACKEND UNLOCK] - Detected as Person:', isPerson);
    
    const requiresWebhook = isPerson && (request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full);
    
    // 🔍 WEBHOOK FLOW LOGGING
    if (requiresWebhook) {
        console.log('🔍 [BACKEND UNLOCK] 📱 WEBHOOK FLOW EXPLANATION:');
        console.log('🔍 [BACKEND UNLOCK] - Phone unlock requires webhook (Apollo requirement)');
        console.log('🔍 [BACKEND UNLOCK] - Apollo will send phone data to webhook URL asynchronously');
        console.log('🔍 [BACKEND UNLOCK] - Lead marked as "pending" until webhook delivers phone data');
        console.log('🔍 [BACKEND UNLOCK] - Check webhook.site to see when Apollo sends data');
        console.log('🔍 [BACKEND UNLOCK] - Phone data will be available after webhook processes');
        console.log('🔍 [BACKEND UNLOCK] ==========================================');
    }
    
    // If webhook is required for people, mark as pending until webhook updates it
    // For companies, mark as successful immediately since data comes back right away
    const isSuccessful = requiresWebhook ? false : isEnrichmentSuccessful;
    
    console.log('🔍 [BACKEND UNLOCK] 📊 UNLOCK SUCCESS ANALYSIS:');
    console.log('🔍 [BACKEND UNLOCK] - Lead Type:', isPerson ? 'PERSON' : 'COMPANY');
    console.log('🔍 [BACKEND UNLOCK] - Enrichment Successful:', isEnrichmentSuccessful);
    console.log('🔍 [BACKEND UNLOCK] - Requires Webhook:', requiresWebhook);
    console.log('🔍 [BACKEND UNLOCK] - Is Successful:', isSuccessful);
    
    // 🔍 HANDLE UNLOCK RECORD: Create new or update existing
    let unlock;
    
    // 🔍 SMART UNLOCK RECORD HANDLING: Handle upgrades and existing records
    const shouldUpdateExisting = existingUnlock && (
        existingUnlock.unlockType === request.unlockType ||
        (existingUnlock.unlockType === UnlockType.Email && request.unlockType === UnlockType.Phone) ||
        (existingUnlock.unlockType === UnlockType.Phone && request.unlockType === UnlockType.Email) ||
        (existingUnlock.unlockType === UnlockType.Email && request.unlockType === UnlockType.Full) ||
        (existingUnlock.unlockType === UnlockType.Phone && request.unlockType === UnlockType.Full)
    );
    
    if (shouldUpdateExisting) {
        // Update existing record (same type or upgrade)
        const unlockTypeDescription = existingUnlock.unlockType === request.unlockType 
            ? 'same unlock type' 
            : `upgrade from ${existingUnlock.unlockType} to ${request.unlockType}`;
            
        console.log('🔍 [BACKEND UNLOCK] 🔄 Updating existing unlock record:', unlockTypeDescription);
        
        const updateSuccess = await leadUnlockService.updateUnlock(existingUnlock.id, {
            apolloData: enrichedData,
            enrichedData: enrichedData,
            isSuccessful: isSuccessful,
            // Update metadata for webhook handling
            metadata: requiresWebhook ? {
                enrichmentLevel: 'basic',
                apolloRequestId: enrichedData?.apolloRequestId,
                enrichedAt: new Date()
            } : undefined
        });
        
        if (updateSuccess) {
            console.log('🔍 [BACKEND UNLOCK] ✅ Existing unlock record updated successfully');
            // Use the existing unlock object since updateUnlock only returns boolean
            unlock = existingUnlock;
        } else {
            console.log('🔍 [BACKEND UNLOCK] ❌ Failed to update existing unlock record');
            // Fall back to creating new record
            unlock = await leadUnlockService.createUnlock({
        workspaceId,
        leadId,
        unlockedBy: userId,
        unlockType: request.unlockType,
        unlockSource: request.source,
        apolloData: enrichedData,
        enrichedData: enrichedData,
        creditsUsed: 1,
                isSuccessful: isSuccessful,
                unlockedAt: new Date(),
                metadata: requiresWebhook ? {
                    enrichmentLevel: 'basic',
                    apolloRequestId: enrichedData?.apolloRequestId,
                    enrichedAt: new Date()
                } : undefined
            });
            console.log('🔍 [BACKEND UNLOCK] ✅ New unlock record created as fallback');
        }
    } else {
        // Different unlock type or no existing unlock - create new record
        console.log('🔍 [BACKEND UNLOCK] 🆕 Creating new unlock record');
        
        unlock = await leadUnlockService.createUnlock({
        workspaceId,
        leadId,
        unlockedBy: userId,
        unlockType: request.unlockType,
        unlockSource: request.source,
        apolloData: enrichedData,
        enrichedData: enrichedData,
        creditsUsed: 1,
            isSuccessful: isSuccessful,
            unlockedAt: new Date(),
            // Add metadata for webhook handling
            metadata: requiresWebhook ? {
                enrichmentLevel: 'basic',
                apolloRequestId: enrichedData?.apolloRequestId,
                enrichedAt: new Date()
            } : undefined
        });
        
        console.log('🔍 [BACKEND UNLOCK] ✅ New unlock record created');
    }
    
    console.log('🔍 [BACKEND UNLOCK] 📊 UNLOCK RECORD CREATED:');
    console.log('🔍 [BACKEND UNLOCK] - Unlock ID:', unlock.id);
    console.log('🔍 [BACKEND UNLOCK] - Is Successful:', isSuccessful);
    console.log('🔍 [BACKEND UNLOCK] - Requires Webhook:', requiresWebhook);
    console.log('🔍 [BACKEND UNLOCK] - Enrichment Successful:', isEnrichmentSuccessful);
    console.log('🔍 [BACKEND UNLOCK] - Credits Used:', unlock.creditsUsed);
    console.log('🔍 [BACKEND UNLOCK] - Unlocked At:', unlock.unlockedAt);
    console.log('🔍 [BACKEND UNLOCK] ==========================================');

    // Normalize enriched data with visibility flags
    const normalizedEnrichedData = {
        ...enrichedData,
        normalizedData: {
            // For people: use personal email/phone
            // For companies: use company phone (no personal emails)
            email: enrichedData?.person?.email || null, // Companies don't have personal emails
            phone: enrichedData?.person?.phone || enrichedData?.organization?.phone || enrichedData?.organization?.primary_phone?.number,
            isEmailVisible: isPerson && (request.unlockType === UnlockType.Email || request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full),
            isPhoneVisible: request.unlockType === UnlockType.Phone || request.unlockType === UnlockType.Full,
        }
    };
    
    await leadService.markAsUnlocked(leadId, normalizedEnrichedData);

    // 🔧 FIX: Fetch the updated lead data after saving
    const updatedLead = await leadService.findOne({ id: leadId, workspaceId });
    
    console.log('🔍 [BACKEND UNLOCK] 📊 UPDATED LEAD DATA:');
    console.log('🔍 [BACKEND UNLOCK] - Updated Lead ID:', updatedLead?.id);
    console.log('🔍 [BACKEND UNLOCK] - Is Unlocked:', updatedLead?.isUnlocked);
    console.log('🔍 [BACKEND UNLOCK] - Has Apollo Data:', !!updatedLead?.apolloData);
    console.log('🔍 [BACKEND UNLOCK] - Has Normalized Data:', !!updatedLead?.normalizedData);
    
    if (updatedLead?.normalizedData) {
        console.log('🔍 [BACKEND UNLOCK] 📊 UPDATED NORMALIZED DATA:');
        console.log('🔍 [BACKEND UNLOCK] - Email:', updatedLead.normalizedData.email);
        console.log('🔍 [BACKEND UNLOCK] - Phone:', updatedLead.normalizedData.phone);
        console.log('🔍 [BACKEND UNLOCK] - Email Visible:', updatedLead.normalizedData.isEmailVisible);
        console.log('🔍 [BACKEND UNLOCK] - Phone Visible:', updatedLead.normalizedData.isPhoneVisible);
    }

    // 🔍 DETERMINE RESPONSE: New unlock or updated existing
    const isNewUnlock = !existingUnlock || existingUnlock.unlockType !== request.unlockType;
    
    console.log('🔍 [BACKEND UNLOCK] 📤 RESPONSE SUMMARY:');
    console.log('🔍 [BACKEND UNLOCK] - Is New Unlock:', isNewUnlock);
    console.log('🔍 [BACKEND UNLOCK] - Already Unlocked:', !isNewUnlock);
    
    // 🔍 WEBHOOK STATUS LOGGING
    if (requiresWebhook) {
        console.log('🔍 [BACKEND UNLOCK] 📱 WEBHOOK STATUS:');
        console.log('🔍 [BACKEND UNLOCK] - Lead marked as "pending" in database');
        console.log('🔍 [BACKEND UNLOCK] - Phone data will arrive via webhook');
        console.log('🔍 [BACKEND UNLOCK] - Check webhook.site for incoming data');
        console.log('🔍 [BACKEND UNLOCK] - Lead will show "pending" until webhook processes');
        console.log('🔍 [BACKEND UNLOCK] - After webhook: phone data will be visible');
        console.log('🔍 [BACKEND UNLOCK] ==========================================');
    }
    
    console.log('🔍 [BACKEND UNLOCK] ==========================================');

    return {
        lead: updatedLead,  // 🔧 FIX: Return the updated lead data
        unlock,
        alreadyUnlocked: !isNewUnlock
    };
};


export const SaveSearch = async (userId: string, workspaceId: string,request: SaveSearchRequest) => {
    console.log('💾 [BACKEND SAVE SEARCH] 🚀 Starting save search process')
    console.log('💾 [BACKEND SAVE SEARCH] 📊 Request data:', {
        userId,
        workspaceId,
        name: request.name,
        description: request.description,
        searchType: request.searchType,
        hasFilters: !!request.filters,
        searchId: request.searchId
    })
    
    // All validation removed - trust user input and let business logic handle validation
    
    if (!request.name || typeof request.name !== 'string' || request.name.trim().length === 0) {
        console.log('💾 [BACKEND SAVE SEARCH] ❌ Invalid search name')
        throw new RequiredParameterError('Search name is required');
    }
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        console.log('💾 [BACKEND SAVE SEARCH] ❌ No workspace member found')
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        console.log('💾 [BACKEND SAVE SEARCH] ❌ No permission to update workspace')
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    console.log('💾 [BACKEND SAVE SEARCH] ✅ Authorization passed')
    
    const leadSearchService = new LeadSearchService();
    
    let searchRecord: LeadSearch;
    
    if (request.searchId) {
        console.log('💾 [BACKEND SAVE SEARCH] 🔄 Updating existing search:', request.searchId)
        
        searchRecord = await leadSearchService.findOne({ id: request.searchId, workspaceId });
        
        if (!searchRecord) {
            console.log('💾 [BACKEND SAVE SEARCH] ❌ Search record not found for ID:', request.searchId)
            throw new NotfoundError(ErrorMessage.EntityNotFound);
        }
        
        console.log('💾 [BACKEND SAVE SEARCH] 📝 Found existing record, updating...')
        
        // Update existing record
        searchRecord.name = request.name;
        searchRecord.description = request.description;
        searchRecord.isSaved = true;
        searchRecord.searchType = request.searchType || 'people';
        searchRecord.updatedById = userId;

        await leadSearchService.update({ id: searchRecord.id }, searchRecord);
        console.log('💾 [BACKEND SAVE SEARCH] ✅ Updated existing search record')
    } else {
        console.log('💾 [BACKEND SAVE SEARCH] 🆕 Creating new search record')
        
        const searchHash = generateSearchHash(request.filters);
        console.log('💾 [BACKEND SAVE SEARCH] 🔑 Generated search hash:', searchHash)
        
        searchRecord = new LeadSearch();
        searchRecord.workspaceId = workspaceId;
        searchRecord.searchHash = searchHash;
        searchRecord.filters = request.filters;
        searchRecord.pagination = { page: 1, limit: 50 }; // Add required pagination field
        searchRecord.name = request.name;
        searchRecord.description = request.description;
        searchRecord.isSaved = true;
        searchRecord.searchType = request.searchType || 'people';
        searchRecord.createdById = userId;
        searchRecord.updatedById = userId;

        // Insert new record
        await leadSearchService.insert(searchRecord);
        console.log('💾 [BACKEND SAVE SEARCH] ✅ Inserted new search record with ID:', searchRecord.id)
    }

    console.log('💾 [BACKEND SAVE SEARCH] 🎉 Save search completed successfully!')
    console.log('💾 [BACKEND SAVE SEARCH] 📋 Final record:', {
        id: searchRecord.id,
        name: searchRecord.name,
        searchType: searchRecord.searchType,
        isSaved: searchRecord.isSaved,
        workspaceId: searchRecord.workspaceId
    })

    return searchRecord;
};

interface GetSavedSearchesOptions {
    page: number;
    limit: number;
    search?: string;
    searchType?: 'people' | 'company';
}


export const GetSavedSearches = async (userId: string, workspaceId: string, options: GetSavedSearchesOptions) => {
    console.log('🔍 [BACKEND GET SAVED SEARCHES] 🚀 Starting get saved searches process')
    console.log('🔍 [BACKEND GET SAVED SEARCHES] 📊 Request data:', {
        userId,
        workspaceId,
        page: options.page,
        limit: options.limit,
        search: options.search
    })
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        console.log('🔍 [BACKEND GET SAVED SEARCHES] ❌ No workspace member found')
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        console.log('🔍 [BACKEND GET SAVED SEARCHES] ❌ No permission to read workspace')
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    console.log('🔍 [BACKEND GET SAVED SEARCHES] ✅ Authorization passed')
    
    const { page, limit, search, searchType } = options;
    const leadSearchService = new LeadSearchService();

    console.log('🔍 [BACKEND GET SAVED SEARCHES] 🔍 Calling leadSearchService.getSavedSearches...')
    const result = await leadSearchService.getSavedSearches(workspaceId, { page, limit, search, searchType });
    
    console.log('🔍 [BACKEND GET SAVED SEARCHES] 📋 Service result:', {
        searchesCount: result.searches?.length || 0,
        total: result.total,
        hasNextPage: (page * limit) < result.total
    })
    
    if (result.searches && result.searches.length > 0) {
        console.log('🔍 [BACKEND GET SAVED SEARCHES] 📝 First search details:', {
            id: result.searches[0].id,
            name: result.searches[0].name,
            searchType: result.searches[0].searchType,
            isSaved: result.searches[0].isSaved
        })
    }

    const response = {
        searches: result.searches,
        totalCount: result.total,
        hasNextPage: (page * limit) < result.total
    };
    
    console.log('🔍 [BACKEND GET SAVED SEARCHES] 🎉 Returning response:', response)
    return response;
};

export const GetSavedSearch = async (userId: string, workspaceId: string, searchId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadSearchService = new LeadSearchService();
    const search = await leadSearchService.findOne({ id: searchId, workspaceId, isSaved: true });
    
    if (!search) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    return search;
};

export const UpdateSavedSearch = async (userId: string, workspaceId: string, searchId: string, updateData: SearchUpdateData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadSearchService = new LeadSearchService();
    const search = await leadSearchService.findOne({ id: searchId, workspaceId });
    
    if (!search) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    Object.assign(search, updateData);
    search.updatedById = userId;
    
    return await leadSearchService.update({ id: searchId }, search);
};

export const DeleteSavedSearch = async (userId: string, workspaceId: string, searchId: string) => {
    console.log('🗑️ [BACKEND DELETE SEARCH] 🚀 Starting delete search process')
    console.log('🗑️ [BACKEND DELETE SEARCH] 📊 Request data:', {
        userId,
        workspaceId,
        searchId
    })

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        console.log('🗑️ [BACKEND DELETE SEARCH] ❌ No workspace member found')
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        console.log('🗑️ [BACKEND DELETE SEARCH] ❌ No permission to update workspace')
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    console.log('🗑️ [BACKEND DELETE SEARCH] ✅ Authorization passed')
    
    const leadSearchService = new LeadSearchService();
    
    // Find the search first to verify it exists
    const search = await leadSearchService.findByWorkspaceAndSearchId(workspaceId, searchId);
    if (!search) {
        console.log('🗑️ [BACKEND DELETE SEARCH] ❌ Search not found')
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    console.log('🗑️ [BACKEND DELETE SEARCH] 🔍 Found search to delete:', {
        id: search.id,
        name: search.name,
        searchType: search.searchType,
        isSaved: search.isSaved
    })
    
    // Actually delete the search from the database (hard delete)
    const result = await leadSearchService.removeSearch(searchId);
    
    if (!result) {
        console.log('🗑️ [BACKEND DELETE SEARCH] ❌ Failed to delete search')
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    console.log('🗑️ [BACKEND DELETE SEARCH] ✅ Successfully deleted search from database')
};


export const DeleteLead = async (userId: string, workspaceId: string, leadId: string) => {
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    
    
    const lead = await leadService.findOne({ id: leadId, workspaceId });
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    const result = await leadService.removeLead(leadId);
    
    if (!result) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    return { success: true, deletedAt: new Date() };
};

interface ExecuteSavedSearchOptions {
    pagination: SearchPagination;
    searchType: 'people' | 'company';
}

export const ExecuteSavedSearch = async (userId: string, workspaceId: string, searchId: string, options: ExecuteSavedSearchOptions) => {

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const search = await GetSavedSearch(userId, workspaceId, searchId);
    
    // Use the correct search function based on the searchType parameter
    if (options.searchType === 'company') {
        return await SearchCompanyLeads(userId, workspaceId, {
            filters: search.filters,
            pagination: options.pagination,
            excludeMyLeads: false
        });
    } else {
        // Default to people search
        return await SearchPeopleLeads(userId, workspaceId, {
            filters: search.filters,
            pagination: options.pagination,
            excludeMyLeads: false
        });
    }
};

export const GetLead = async (userId: string, workspaceId: string, leadId: string) => {

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const lead = await leadService.findOne({ id: leadId, workspaceId });
    
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    return lead;
};

export const UpdateLead = async (userId: string, workspaceId: string, leadId: string, updateData: LeadUpdateData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const lead = await leadService.findOne({ id: leadId, workspaceId });
    
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    Object.assign(lead, updateData);
    lead.updatedById = userId;
    
    return await leadService.update({ id: leadId }, lead);
};

export const VoteLead = async (userId: string, workspaceId: string, leadId: string, request: LeadVoteRequest) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const lead = await GetLead(userId, workspaceId, leadId);
    
    if (!lead.isUnlocked) {
        throw new UnauthorizedError('Lead must be unlocked before voting');
    }
    
    if (!request.feedback || request.feedback.trim().length === 0) {
        throw new RequiredParameterError('Feedback is required for voting');
    }
    
    const currentMeta: LeadMeta = (lead.meta as LeadMeta) || {};
    
    const existingVotes: LeadVoteFeedback[] = currentMeta.votes || [];
    
    const newVote: LeadVoteFeedback = {
        userId,
        vote: request.vote,
        feedback: request.feedback.trim(),
        feedbackType: request.feedbackType,
        votedAt: new Date()
    };
    
    const newMeta: LeadMeta = {
        ...currentMeta,
        votes: [...existingVotes, newVote]
    };
    
    lead.meta = newMeta;
    
    const leadService = new LeadService();
    return await leadService.update({ id: lead.id }, lead);
};

export const GetVoteFeedbackOptions = () => {
    return PREDEFINED_FEEDBACK_OPTIONS;
};

export const BulkUnlockLeads = async (userId: string, workspaceId: string, leadIds: string[], request: BulkUnlockRequest) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const leadUnlockService = new LeadUnlockService();
    
    const leads = await leadService.findByWorkspaceAndIds(workspaceId, leadIds);
    const leadsNeedingEnrichment = leads.filter(lead => !lead.email || !lead.normalizedData?.phone);
    
    if (leadsNeedingEnrichment.length > 0) {
        const peopleLeads = leadsNeedingEnrichment.filter(lead => lead.type === LeadType.Person);
        const companyLeads = leadsNeedingEnrichment.filter(lead => lead.type === LeadType.Company);
        
        if (peopleLeads.length > 0) {
            const emails = peopleLeads.map(lead => lead.email).filter(Boolean);
            if (emails.length > 0) {
                try {
                    const enrichedPeople = await ExecuteWorkspaceIntegrationActionNoAuth(
                        workspaceId,
                        'apollo',
                        {
                            name: 'bulkPeopleEnrichment',
                            type: 'action',
                            mode: 'run',
                            propsValue: { emails: emails.slice(0, 10) } // Apollo limits to 10
                        },
                        null
                    );
                    
                    for (const enrichedPerson of enrichedPeople.result.people) {
                        const lead = peopleLeads.find(l => l.email === enrichedPerson.email);
                        if (lead) {
                            await leadService.update({ id: lead.id }, {
                                email: enrichedPerson.email,
                                normalizedData: { ...lead.normalizedData, phone: enrichedPerson.phone },
                                meta: { ...lead.meta, enriched: true }
                            });
                        }
                    }
                } catch (error) {
                    consoleLog('Bulk people enrichment failed:', error);
                }
            }
        }
        
        if (companyLeads.length > 0) {
            const domains = companyLeads.map(lead => lead.companyDomain).filter(Boolean);
            if (domains.length > 0) {
                try {
                    const enrichedCompanies = await ExecuteWorkspaceIntegrationActionNoAuth(
                        workspaceId,
                        'apollo',
                        {
                            name: 'bulkOrganizationEnrichment',
                            type: 'action',
                            mode: 'run',
                            propsValue: { domains: domains.slice(0, 10) } // Apollo limits to 10
                        },
                        null
                    );
                    
                    for (const enrichedCompany of enrichedCompanies.result.organizations) {
                        const lead = companyLeads.find(l => l.companyDomain === enrichedCompany.domain);
                        if (lead) {
                            await leadService.update({ id: lead.id }, {
                                meta: { ...lead.meta, enriched: true, companyData: enrichedCompany }
                            });
                        }
                    }
                } catch (error) {
                    consoleLog('Bulk company enrichment failed:', error);
                }
            }
        }
    }
    
    const results = [];
    
    for (const leadId of leadIds) {
        try {
            const result = await UnlockLead(userId, workspaceId, leadId, request);
            results.push({ leadId, success: true, result });
        } catch (error) {
            results.push({ leadId, success: false, error: error.message });
        }
    }
    
    return {
        total: leadIds.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        results
    };
};

export interface CreateLeadInApolloRequest {
    firstName: string;
    lastName: string;
    email: string;
    companyDomain?: string;
    jobTitle?: string;
    meta?: Record<string, unknown>;
}

export const CreateLeadInApollo = async (userId: string, workspaceId: string, leadData: CreateLeadInApolloRequest) => {
    
    // All validation removed - trust user input and let business logic handle validation
    if (!leadData.firstName || typeof leadData.firstName !== 'string' || leadData.firstName.trim().length === 0) {
        throw new RequiredParameterError('firstName is required and must be a non-empty string');
    }
    if (!leadData.lastName || typeof leadData.lastName !== 'string' || leadData.lastName.trim().length === 0) {
        throw new RequiredParameterError('lastName is required and must be a non-empty string');
    }
    if (!leadData.email || typeof leadData.email !== 'string' || leadData.email.trim().length === 0) {
        throw new RequiredParameterError('email is required and must be a non-empty string');
    }
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    
    try {
        const apolloContact = await ExecuteWorkspaceIntegrationActionNoAuth(
            workspaceId,
            'apollo',
            {
                name: 'createContact',
                type: 'action',
                mode: 'run',
                propsValue: {
                    first_name: leadData.firstName,
                    last_name: leadData.lastName,
                    email: leadData.email
                }
            },
            null
        );
        
        if (!apolloContact?.result?.contact?.id) {
            throw new BadRequestError('Invalid response from Apollo createContact action');
        }
        
        const normalizedData: NormalizedLeadData = {
            id: apolloContact.result.contact.id,
            name: `${leadData.firstName} ${leadData.lastName}`,
            email: leadData.email,
            phone: undefined,
            jobTitle: leadData.jobTitle,
            company: undefined,
            companyDomain: leadData.companyDomain,
            linkedinUrl: undefined,
            photoUrl: undefined,
            location: undefined,
            isEmailVisible: true,
            isPhoneVisible: false,
            confidence: undefined
        };
        
                    // Normalized data validation removed - trust Apollo's data quality
        
        const lead = await leadService.createLead({
            apolloId: apolloContact.result.contact.id,
            type: LeadType.Person,
            source: LeadSource.Manual,
            apolloData: apolloContact.result.contact,
            normalizedData,
            searchHashes: [],
            email: leadData.email,
            name: normalizedData.name,
            companyDomain: leadData.companyDomain,
            createdById: userId,
            workspaceId
        });
        
        return {
            lead,
            apolloContact: apolloContact.result.contact,
            success: true
        };
    } catch (error) {
        throw new ServerProcessingError(`Failed to create lead in Apollo: ${error.message}`);
    }
};

 
export const SearchCompanyLeadsWithBulkEnrichment = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
 
    
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { filters, pagination, excludeMyLeads } = request;
    
    const MAX_BULK_ENRICHMENT = 10;
    
    if (filters.customFilters?.companyDomains && Array.isArray(filters.customFilters.companyDomains) && filters.customFilters.companyDomains.length > 0) {
        try {
            const enrichedCompanies = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'bulkOrganizationEnrichment',
                    type: 'action',
                    mode: 'run',
                    propsValue: { domains: filters.customFilters.companyDomains.slice(0, MAX_BULK_ENRICHMENT) }
                },
                null
            );
            
            if (!enrichedCompanies?.result?.organizations || !Array.isArray(enrichedCompanies.result.organizations)) {
                throw new BadRequestError('Invalid response from Apollo bulkOrganizationEnrichment action');
            }
            
            const enrichedLeads = enrichedCompanies.result.organizations.map((company: ApolloCompanyData) => ({
                apolloId: company.id,
                type: LeadType.Company,
                name: company.name,
                companyDomain: company.domain,
                source: LeadSource.Apollo,
                meta: {
                    industry: company.industry,
                    size: company.size,
                    location: company.location,
                    technologies: company.technologies,
                    funding: company.funding,
                    enriched: true
                },
                workspaceId
            }));
            
            await processAndStoreLeads({workspaceId, apolloResults: enrichedLeads, searchHash: '', userId, leadType: LeadType.Company});
            
            const processedLeads = await processLeadsForResponse(enrichedLeads, excludeMyLeads, userId, workspaceId);
            
            return {
                leads: processedLeads,
                totalCount: enrichedLeads.length,
                hasNextPage: false,
                searchId: null,
                filters,
                metadata: { apolloRequestId: 'bulk_enrichment', apolloCreditsUsed: 1, processingTimeMs: 0, resultQuality: 'high', dataFreshness: new Date() }
            };
        } catch (error) {
            consoleLog('Bulk company enrichment failed:', error);
        }
    }
    
    return SearchLeadsByType(userId, workspaceId, request, LeadType.Company);
};


export const SearchPeopleLeadsWithBulkEnrichment = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
 
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { filters, pagination, excludeMyLeads } = request;
    
    const MAX_BULK_ENRICHMENT = 10;
    
    if (filters.customFilters?.emails && Array.isArray(filters.customFilters.emails) && filters.customFilters.emails.length > 0) {
        try {
            const enrichedPeople = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'bulkPeopleEnrichment',
                    type: 'action',
                    mode: 'run',
                    propsValue: { emails: filters.customFilters.emails.slice(0, MAX_BULK_ENRICHMENT) }
                },
                null
            );
            
            if (!enrichedPeople?.result?.people || !Array.isArray(enrichedPeople.result.people)) {
                throw new BadRequestError('Invalid response from Apollo bulkPeopleEnrichment action');
            }
            
            const enrichedLeads = enrichedPeople.result.people.map((person: ApolloPersonData) => ({
                apolloId: person.id,
                type: LeadType.Person,
                name: person.name || `${person.first_name || ''} ${person.last_name || ''}`.trim(),
                email: person.email,
                source: LeadSource.Apollo,
                meta: {
                    jobTitle: person.title,
                    company: person.organization?.name,
                    location: person.city ? `${person.city}, ${person.state}` : person.country,
                    linkedinUrl: person.linkedin_url,
                    enriched: true
                },
                workspaceId
            }));
            
            await processAndStoreLeads({workspaceId, apolloResults: enrichedLeads, searchHash: '', userId, leadType: LeadType.Person});
            
            const processedLeads = await processLeadsForResponse(enrichedLeads, excludeMyLeads, userId, workspaceId);
            
            return {
                leads: processedLeads,
                totalCount: enrichedLeads.length,
                hasNextPage: false,
                searchId: null,
                filters,
                metadata: { apolloRequestId: 'bulk_enrichment', apolloCreditsUsed: 1, processingTimeMs: 0, resultQuality: 'high', dataFreshness: new Date() }
            };
        } catch (error) {
            consoleLog('Bulk people enrichment failed:', error);
        }
    }
    
    return SearchLeadsByType(userId, workspaceId, request, LeadType.Person);
};


export interface BulkEnrichLeadsData {
    leadIds: string[];
    enrichmentType?: UnlockType;
}

export const BulkEnrichLeads = async (userId: string, workspaceId: string, bulkEnrichData: BulkEnrichLeadsData) => {
    

    if (!bulkEnrichData.leadIds || !Array.isArray(bulkEnrichData.leadIds) || bulkEnrichData.leadIds.length === 0) {
        throw new RequiredParameterError('leadIds array is required and must not be empty');
    }
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const results = [];
    
    const leads = await leadService.findByWorkspaceAndIds(workspaceId, bulkEnrichData.leadIds);
    const peopleLeads = leads.filter(lead => lead.type === LeadType.Person);
    const companyLeads = leads.filter(lead => lead.type === LeadType.Company);
    
    if (peopleLeads.length > 0) {
        const emails = peopleLeads.map(lead => lead.email).filter(Boolean);
        if (emails.length > 0) {
            try {
                const enrichedPeople = await ExecuteWorkspaceIntegrationActionNoAuth(
                    workspaceId,
                    'apollo',
                    {
                        name: 'bulkPeopleEnrichment',
                        type: 'action',
                        mode: 'run',
                        propsValue: { emails: emails.slice(0, 10) }
                    },
                    null
                );
                
                for (const enrichedPerson of enrichedPeople.result.people) {
                    const lead = peopleLeads.find(l => l.email === enrichedPerson.email);
                    if (lead) {
                        await leadService.update({ id: lead.id }, {
                            normalizedData: { ...lead.normalizedData, phone: enrichedPerson.phone },
                            meta: { ...lead.meta, enriched: true, enrichedAt: new Date().toISOString() }
                        });
                        results.push({ leadId: lead.id, success: true, type: 'person' });
                    }
                }
            } catch (error) {
                if (error instanceof BadRequestError) {
                    throw error;
                }
     
                consoleLog('Bulk people enrichment failed:', error);
                results.push({ leadId: 'bulk_people', success: false, error: error.message, type: 'person' });
            }
        }
    }
    
    // Bulk enrich company leads
    if (companyLeads.length > 0) {
        const domains = companyLeads.map(lead => lead.companyDomain).filter(Boolean);
        if (domains.length > 0) {
            try {
                const enrichedCompanies = await ExecuteWorkspaceIntegrationActionNoAuth(
                    workspaceId,
                    'apollo',
                    {
                        name: 'bulkOrganizationEnrichment',
                        type: 'action',
                        mode: 'run',
                        propsValue: { domains: domains.slice(0, 10) }
                    },
                    null
                );
                
                for (const enrichedCompany of enrichedCompanies.result.organizations) {
                    const lead = companyLeads.find(l => l.companyDomain === enrichedCompany.domain);
                    if (lead) {
                        await leadService.update({ id: lead.id }, {
                            meta: { ...lead.meta, enriched: true, companyData: enrichedCompany, enrichedAt: new Date().toISOString() }
                        });
                        results.push({ leadId: lead.id, success: true, type: 'company' });
                    }
                }
            } catch (error) {
                if (error instanceof BadRequestError) {
                    throw error;
                }
                consoleLog('Bulk company enrichment failed:', error);
                results.push({ leadId: 'bulk_company', success: false, error: error.message, type: 'company' });
            }
        }
    }
    
    return {
        total: bulkEnrichData.leadIds.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        results
    };
};

// Frontend will handle file generation and download

export const ExportLeads = async (userId: string, workspaceId: string, leadIds: string[], request: ExportRequest) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const leads = await leadService.findByWorkspaceAndIds(workspaceId, leadIds);
    
    // Validate export limits
    if (leads.length > 1000) {
        throw new InvalidParameterError('Cannot export more than 1000 leads at once');
    }
    
    // Return data for frontend to handle file generation and download
    return {
        format: request.format,
        leadCount: leads.length,
        data: leads.map(lead => ({
            id: lead.id,
            name: lead.normalizedData?.name,
            email: lead.normalizedData?.email,
            phone: lead.normalizedData?.phone,
            company: lead.normalizedData?.company,
            jobTitle: lead.normalizedData?.jobTitle,
            companyDomain: lead.normalizedData?.companyDomain,
            type: lead.type,
            source: lead.source,
            createdAt: lead.createdAt,
            ...(request.includeFullData ? { apolloData: lead.apolloData } : {})
        }))
    };
};

// ✅ INTERFACE FOR AddLeadToSegment FUNCTION
export interface AddLeadToSegmentData {
    name: string;
    databaseId?: string;
}

// Add lead to segments 
export const AddLeadToSegment = async ( userId: string, workspaceId: string, leadId: string,segmentData: AddLeadToSegmentData) => {
   
    const lead = await GetLead(userId, workspaceId, leadId);
    
   
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    // Use existing database system to create a segment
    const { databases } = await GetMyDatabases(userId, workspaceId);
    
    // Find or create a leads database
    let leadsDatabase = databases.find(db => db.database.name === 'Leads Segment');
    let databaseId: string;
    
    if (!leadsDatabase) {
        // Create a new database for leads segments
        const newDatabase = await CreateDatabase(userId, workspaceId, {
            name: 'Leads Segment'
        });
        databaseId = newDatabase.database.database.id;
    } else {
        databaseId = leadsDatabase.database.id;
    }
    
    // Add lead to the segment database using the correct structure
    const segmentRecord = await AddRecords(userId, workspaceId, databaseId, {
        valuesList: [{
            'Lead ID': leadId,
            'Segment Name': segmentData.name,
            'Lead Name': lead.normalizedData.name,
            'Company': lead.normalizedData.company || '',
            'Job Title': lead.normalizedData.jobTitle || '',
            'Added Date': new Date().toISOString()
        }],
        onDuplicate: OnDuplicateAction.Update
    });
    
    return {
        success: true,
        segmentId: segmentRecord.records[0].id,
        databaseId: databaseId,
        addedAt: new Date()
    };
};


export interface AddLeadToWorkflowData {
    workflowId: string;
    triggerType?: 'manual' | 'immediate';
}

export const AddLeadToWorkflow = async ( userId: string, workspaceId: string, leadId: string, workflowData: AddLeadToWorkflowData) => {
    // Validate lead exists
    const lead = await GetLead(userId, workspaceId, leadId);
    
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    // Use existing workflow system
    const workflows = await GetWorkflows(userId, workspaceId, { id: workflowData.workflowId });
    
    // Find the workflow
    const workflow = workflows.find(w => w.id === workflowData.workflowId);
    if (!workflow) {
        throw new NotfoundError('Workflow not found');
    }
    
    // Check if workflow can be manually triggered
    if (workflow.triggerType !== 'OnDemand_Callable') {
        throw new BadRequestError('Workflow must be OnDemand_Callable to be manually triggered');
    }
    
    // Create a workflow instance with lead data
    const workflowInstance = await CreateWorkflowInstance(userId, workspaceId, workflowData.workflowId, {
        data: {
            leadId,
            leadData: lead,
            triggerSource: 'lead_management'
        }
    });
    
    return {
        success: true,
        workflowId: workflowData.workflowId,
        workflowInstanceId: workflowInstance.id,
        triggeredAt: new Date()
    };
};


export interface AddLeadToDatabaseData {
    targetDatabaseId: string;
}

// Add lead to external database 
export const AddLeadToDatabase = async ( userId: string, workspaceId: string, leadId: string, databaseData: AddLeadToDatabaseData) => {
   
    const lead = await GetLead(userId, workspaceId, leadId);
    
   
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
   
    const { database: targetDatabase } = await GetMyDatabase(userId, workspaceId, databaseData.targetDatabaseId);
    
   
    const recordValues: RecordValues = {};
    
    // Auto-map common fields based on column names
    if (targetDatabase.database.definition && targetDatabase.database.definition.columnsMap) {
        Object.keys(targetDatabase.database.definition.columnsMap).forEach(columnId => {
            const column = targetDatabase.database.definition.columnsMap[columnId];
            // Access column name safely - columns have different structures
            const columnName = typeof column === 'object' && column !== null && 'name' in column 
                ? (column as { name: string }).name 
                : columnId;
            const columnNameLower = columnName.toLowerCase();
            
            if (columnNameLower.includes('name') && !columnNameLower.includes('company')) {
                recordValues[columnName] = lead.normalizedData.name;
            } else if (columnNameLower.includes('company')) {
                recordValues[columnName] = lead.normalizedData.company || '';
            } else if (columnNameLower.includes('email')) {
                recordValues[columnName] = lead.normalizedData.email || '';
            } else if (columnNameLower.includes('phone')) {
                recordValues[columnName] = lead.normalizedData.phone || '';
            } else if (columnNameLower.includes('job') || columnNameLower.includes('title')) {
                recordValues[columnName] = lead.normalizedData.jobTitle || '';
            } else if (columnNameLower.includes('date') || columnNameLower.includes('created')) {
                recordValues[columnName] = new Date().toISOString();
            }
        });
    }
    
    // Add lead to target database
    const result = await AddRecords(userId, workspaceId, databaseData.targetDatabaseId, {
        valuesList: [recordValues],
        onDuplicate: OnDuplicateAction.Update
    });
    
    return {
        success: true,
        databaseId: databaseData.targetDatabaseId,
        recordId: result.records[0].id,
        syncedAt: new Date()
    };
};

    export interface SendEmailToLeadData {
    subject: string;
    body: string;
    template?: string;
}

// Send email to lead (using existing email service)
export const SendEmailToLead = async ( userId: string, workspaceId: string,leadId: string,emailData: SendEmailToLeadData) => {
    // Validate lead exists
    const lead = await GetLead(userId, workspaceId, leadId);
    
    // Check if lead is unlocked
    if (!lead.isUnlocked) {
        throw new UnauthorizedError('Lead must be unlocked before sending email');
    }
    
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    
    const emailResult = await SendEmailWithContent(
        { email: lead.normalizedData.email, name: lead.normalizedData.name },
        emailData.subject,
        emailData.body,
        null, 
        null, 
        true, 
        undefined, 
        undefined,
        undefined, 
        [],
        true,
        [], // cc
        [] // bcc
    );
    
    return {
        success: true,
        emailId: emailResult,
        sentAt: new Date()
    };
};


export const GetLeadsBySearchHashes = async (userId: string, workspaceId: string, searchHashes: string[], pagination?: SearchPagination) => {
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (searchHashes.length === 0) {
        return {
            leads: [],
            totalCount: 0,
            hasNextPage: false
        };
    }
    
    const leadService = new LeadService();

    const leads = await leadService.findByWorkspaceAndSearchHashes(workspaceId, searchHashes);
    
    // If no pagination provided, return all results
    if (!pagination) {
        return {
            leads,
            totalCount: leads.length,
            hasNextPage: false
        };
    }
    
    // Apply pagination
    const { page, limit } = pagination;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLeads = leads.slice(startIndex, endIndex);
    
    return {
        leads: paginatedLeads,
        totalCount: leads.length,
        hasNextPage: (page * limit) < leads.length
    };
};


export const IsLeadUnlocked = async (userId: string, workspaceId: string, leadId: string): Promise<boolean> => {
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadUnlockService = new LeadUnlockService();
    
    // Use new service method
    return await leadUnlockService.isLeadUnlocked(workspaceId, leadId);
};


const getApolloConnectionAPI = (workspaceId: string): ConnectionAPI => {
    return {
        OAuth2Configs: async () => ({
            credentials: {
                clientId: '',
                clientSecret: ''
            },
            redirectUri: ''
        }),
        delete: async () => {},
        save: async () => {},
        get: async (integration: string, id: string): Promise<IntegrationCredentials | null> => {
            if (integration === 'apollo') {
                const apolloApiKey = process.env.APOLLO_API_KEY;
                if (!apolloApiKey) {
                    throw new Error('APOLLO_API_KEY environment variable is not set');
                }
                
                return {
                    id: 'apollo-default',
                    name: 'Apollo Default Connection',
                    integration: 'apollo',
                    credentials: {
                        apiKey: apolloApiKey
                    }
                };
            }
            return null;
        }
    };
};
