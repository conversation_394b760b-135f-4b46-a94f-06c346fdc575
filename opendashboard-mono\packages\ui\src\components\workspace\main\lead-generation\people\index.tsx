"use client"

import React, { useState, useRef, useMemo } from "react"
import MyLeads from "./my-leads"
import FindLeads from "./find-leads"
import SavedSearch from "./saved-search"
import { useWorkspace } from "@ui/providers/workspace"
import { useAuth } from "@ui/providers/user"
import { But<PERSON> } from "@ui/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuGroup } from "@ui/components/ui/dropdown-menu"
import { EnvelopeIcon, ChartLineIcon, DatabaseIcon, CodeMergeIcon, UpRightFromSquareIcon } from "@ui/components/icons/FontAwesomeRegular"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@ui/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/ui/table"
import { Checkbox } from "@ui/components/ui/checkbox"
import { addLeadToDatabase, addLeadToSegment, addLeadToWorkflow, sendEmailToLead } from "@ui/api/leads"
import { getDatabases } from "@ui/api/database"
import { useAlert } from "@ui/providers/alert"
import { useRouter, useParams } from "@ui/context/routerContext"
import { UnlockEmailModal } from "@ui/components/workspace/main/common/unlockEmail"
import { UnlockPhoneModal } from "@ui/components/workspace/main/common/unlockPhone"

interface PeopleProps {
    activeSubTab: 'my-leads' | 'find-leads' | 'saved-search'
    onLeadCreated?: (lead: any) => void
}

// Shared Components
export const ActionButton = ({ 
    icon: Icon, 
    children, 
    onClick 
}: { 
    icon: React.ComponentType<{ className?: string }>; 
    children: React.ReactNode; 
    onClick: () => void; 
}) => (
    <Button
        variant="outline"
        size="sm"
        onClick={onClick}
        className="text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center"
    >
        <Icon className="size-3" />
        <span className="truncate">{children}</span>
    </Button>
)

export const ViewLinksModal = ({ 
    trigger,
    links 
}: { 
    trigger: React.ReactNode;
    links: Array<{ id: string; title: string; url: string }> 
}) => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="w-64 p-4" align="end">
                <h4 className="font-semibold text-sm mb-2">Social Links</h4>
                <div className="space-y-2">
                    {links.length === 0 ? (
                        <div className="text-xs text-muted-foreground">No links available</div>
                    ) : (
                        links.map((link) => (
                            <a
                                key={link.id}
                                href={link.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors p-2 rounded hover:bg-blue-50"
                            >
                                <UpRightFromSquareIcon className="size-3" />
                                <span className="truncate">{link.title}</span>
                            </a>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    )
}

interface LeadActionsDropdownProps {
    trigger: React.ReactNode
    onSendEmail?: () => void
    onAddToSegments?: () => void
    onAddToDatabase?: () => void
    onAddToWorkflow?: () => void
}

export const LeadActionsDropdown = ({ 
    trigger, 
    onSendEmail, 
    onAddToSegments, 
    onAddToDatabase, 
    onAddToWorkflow 
}: LeadActionsDropdownProps) => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {trigger}
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                className="w-56 rounded-none text-neutral-800 font-semibold"
                align="end"
                sideOffset={4}
            >
                <DropdownMenuGroup className="p-1 flex flex-col gap-2">
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onSendEmail}
                    >
                        <EnvelopeIcon className="size-3 text-neutral-600" />
                        <span>Send Email</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToSegments}
                    >
                        <ChartLineIcon className="size-3 text-neutral-600" />
                        <span>Add to Segments</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToDatabase}
                    >
                        <DatabaseIcon className="size-3 text-neutral-600" />
                        <span>Add to Database</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem 
                        className="text-xs rounded-none p-2 py-1.5 flex items-center gap-2 cursor-pointer"
                        onClick={onAddToWorkflow}
                    >
                        <CodeMergeIcon className="size-3 text-neutral-600" />
                        <span>Add to Workflow</span>
                    </DropdownMenuItem>
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

// Shared table header component (people version)
export const PeopleTableHeader = ({ 
    selectedLeads, 
    filteredLeads, 
    handleSelectAll 
}: { 
    selectedLeads: string[]
    filteredLeads: any[]
    handleSelectAll: (checked: boolean, leads: any[]) => void
}) => (
    <TableHeader>
        <TableRow className="border-b border-neutral-200 bg-white sticky top-0 z-10">
            <TableHead className="w-12 h-10 px-3">
                <Checkbox
                    checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0}
                    onCheckedChange={(checked: boolean) => handleSelectAll(checked, filteredLeads)}
                />
            </TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Name</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Job title</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Company</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Location</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Email</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Phone number</TableHead>
            <TableHead className="h-10 px-1 text-left font-bold text-black">Social Links</TableHead>
            <TableHead className="w-12 h-10 px-1"></TableHead>
        </TableRow>
    </TableHeader>
)

// Centralized hook for all lead-related actions, state, and utilities
export const useLeadManagement = () => {
    const { toast } = useAlert()
    const { token } = useAuth()
    const { workspace } = useWorkspace()
    const router = useRouter()
    const params = useParams()
    
    // All shared state variables
    const [selectedLeads, setSelectedLeads] = useState<string[]>([])
    const [emailModalOpen, setEmailModalOpen] = useState(false)
    const [phoneModalOpen, setPhoneModalOpen] = useState(false)
    const [selectedLeadId, setSelectedLeadId] = useState<string | null>(null)
    
    // Callback refs for unlock success
    const unlockEmailCallbackRef = useRef<((data: any) => void) | null>(null)
    const unlockPhoneCallbackRef = useRef<((data: any) => void) | null>(null)
    
    // Add to Database state
    const [addToDatabaseDialogOpen, setAddToDatabaseDialogOpen] = useState(false)
    const [selectedDatabaseId, setSelectedDatabaseId] = useState<string>('')
    const [availableDatabases, setAvailableDatabases] = useState<any[]>([])
    const [loadingDatabases, setLoadingDatabases] = useState(false)
    const [addingToDatabase, setAddingToDatabase] = useState(false)
    const [selectedLeadIdForAction, setSelectedLeadIdForAction] = useState<string | null>(null)
    const [dialogKey, setDialogKey] = useState(0) // Force re-render key
    
    // Shared selection handlers
    const handleSelectAll = (checked: boolean, leads: any[]) => {
        if (checked) {
            setSelectedLeads(leads.map(lead => lead.id))
        } else {
            setSelectedLeads([])
        }
    }

    const handleSelectLead = (leadId: string, checked: boolean) => {
        if (checked) {
            setSelectedLeads([...selectedLeads, leadId])
        } else {
            setSelectedLeads(selectedLeads.filter(id => id !== leadId))
        }
    }

    // Shared unlock handlers
    const handleUnlockEmail = (leadId: string) => {
        setSelectedLeadId(leadId)
        setEmailModalOpen(true)
    }

    const handleUnlockPhone = (leadId: string) => {
        setSelectedLeadId(leadId)
        setPhoneModalOpen(true)
    }

    // Shared navigation handlers
    const handleNameClick = (lead: any) => {
        const domain = params.domain
        router.push(`/${domain}/lead-generation/people/details/${lead.id}`)
    }

    const handleViewLinks = (leadId: string) => {
        console.log("View links for lead:", leadId)
    }

    // Shared contact links generation (people version)
    const getContactLinks = (lead: any) => {
        const links = []
        
        // Add LinkedIn search if name is available
        if (lead.name) {
            const searchQuery = lead.company 
                ? `${lead.name} ${lead.company}` 
                : lead.name
            links.push({ 
                id: "linkedin", 
                title: "LinkedIn Profile", 
                url: `https://linkedin.com/search/results/people/?keywords=${encodeURIComponent(searchQuery)}` 
            })
        }
        
        // Add company website search if company is available
        if (lead.company) {
            links.push({ 
                id: "company", 
                title: "Company Website", 
                url: `https://www.google.com/search?q=${encodeURIComponent(lead.company + ' official website')}` 
            })
        }
        
        // Add Google search for the person
        if (lead.name) {
            const searchQuery = lead.company 
                ? `${lead.name} ${lead.company}` 
                : lead.name
            links.push({ 
                id: "google", 
                title: "Google Search", 
                url: `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}` 
            })
        }
        
        // Add Twitter/X search if name is available
        if (lead.name) {
            links.push({ 
                id: "twitter", 
                title: "Twitter/X Profile", 
                url: `https://twitter.com/search?q=${encodeURIComponent(lead.name)}` 
            })
        }
        
        return links
    }

    // Shared import leads handler
    const handleImportLeads = () => {
        console.log("Import leads clicked")
    }

    // Shared API-to-UI conversion logic (people version)
    const convertApiLeadsToUI = (apiLeads: any[]): any[] => {
        return apiLeads.map((apiLead) => ({
            id: apiLead.id,
            name: apiLead.normalizedData.name,
            jobTitle: apiLead.normalizedData.jobTitle || "",
            company: apiLead.normalizedData.company || "",
            email: apiLead.normalizedData.isEmailVisible ? apiLead.normalizedData.email || "unlock" : "unlock",
            phone: apiLead.normalizedData.isPhoneVisible ? apiLead.normalizedData.phone || "unlock" : "unlock",
            links: "view",
            // Add location data
            location: apiLead.normalizedData?.location ? 
                [apiLead.normalizedData.location.city, apiLead.normalizedData.location.state, apiLead.normalizedData.location.country]
                    .filter(Boolean).join(', ') || '-'
                : (apiLead.apolloData && 'city' in apiLead.apolloData && apiLead.apolloData.city && apiLead.apolloData.state && apiLead.apolloData.country) ?
                    [apiLead.apolloData.city, apiLead.apolloData.state, apiLead.apolloData.country].filter(Boolean).join(', ')
                    : '-'
        }))
    }

    // Shared filtered leads logic (people version)
    const getFilteredLeads = (leads: any[], searchQuery?: string, filter?: any) => {
        return useMemo(() => {
            let filtered = leads
            
            // Apply search filter if user is searching locally
            if (searchQuery?.trim()) {
                const searchTerm = searchQuery.trim().toLowerCase()
                filtered = filtered.filter(lead => 
                    Object.values(lead).some(value => 
                        typeof value === 'string' && value.toLowerCase().includes(searchTerm)
                    )
                )
            }
            
            // Apply filter conditions (if any filters are set)
            if (filter?.conditions?.length > 0) {
                filtered = filtered.filter(lead => {
                    return filter.conditions.every((condition: any) => {
                        const value = condition.value?.toString().toLowerCase() || ''
                        const leadValue = lead[condition.columnId as keyof any]?.toString().toLowerCase() || ''
                        return leadValue.includes(value)
                    })
                })
            }
            
            return filtered
        }, [leads, searchQuery, filter])
    }

    const handleSendEmail = async (leadId: string) => {
        try {
            await sendEmailToLead(leadId, token?.token || '', workspace?.workspace?.id || '', {
                subject: "Hello from OpenDashboard",
                body: "This is an automated email from OpenDashboard."
            })
            toast.success("Email sent successfully!")
        } catch (error) {
            console.error("Failed to send email:", error)
            toast.error("Failed to send email")
        }
    }

    const handleAddToSegments = async (leadId: string) => {
        try {
            await addLeadToSegment(token?.token || '', workspace?.workspace?.id || '', leadId, { name: "default-segment" }) // TODO: Add segment selection
            toast.success("Lead added to segment successfully!")
        } catch (error) {
            console.error("Failed to add to segment:", error)
            toast.error("Failed to add to segment")
        }
    }

    const handleAddToDatabase = async (leadId: string) => {
        console.log("🔍 handleAddToDatabase called with leadId:", leadId)
        console.log("🔍 Token:", token?.token)
        console.log("🔍 Workspace ID:", workspace?.workspace?.id)
        
        // First, fetch databases
        setLoadingDatabases(true)
        setSelectedLeadIdForAction(leadId)
        
        try {
            console.log("🔍 Fetching databases...")
            const response = await getDatabases(token?.token || '', workspace?.workspace?.id || '')
            console.log("🔍 Databases response:", response)
            console.log("🔍 Response data:", response.data)
            console.log("🔍 Response data.data:", response.data?.data)
            console.log("🔍 Response data.data.databases:", response.data?.data?.databases)
            console.log("🔍 Response error:", response.error)
            
            if (response.error) {
                console.error("🔍 API Error:", response.error)
                toast.error(`Failed to fetch databases: ${typeof response.error === 'string' ? response.error : 'Unknown error'}`)
                return
            }
            
            const databases = response.data?.data?.databases || []
            console.log("🔍 Setting databases:", databases)
            console.log("🔍 Database count:", databases.length)
            
            // Set all states and force re-render
            setAvailableDatabases(databases)
            setAddToDatabaseDialogOpen(true)
            setDialogKey(prev => prev + 1) // Force dialog re-render
            
            console.log("🔍 States set - databases:", databases.length, "dialog: true, key:", dialogKey + 1)
            
        } catch (error) {
            console.error("Failed to fetch databases:", error)
            toast.error("Failed to fetch databases")
        } finally {
            setLoadingDatabases(false)
        }
    }

    const handleConfirmAddToDatabase = async () => {
        if (!selectedLeadIdForAction || !selectedDatabaseId) return
        
        setAddingToDatabase(true)
        try {
            await addLeadToDatabase(token?.token || '', workspace?.workspace?.id || '', selectedLeadIdForAction, { targetDatabaseId: selectedDatabaseId })
            toast.success("Lead added to database successfully!")
            setAddToDatabaseDialogOpen(false)
            setSelectedDatabaseId('')
            setSelectedLeadIdForAction(null)
        } catch (error) {
            console.error("Failed to add to database:", error)
            toast.error("Failed to add to database")
        } finally {
            setAddingToDatabase(false)
        }
    }

    const handleAddToWorkflow = async (leadId: string) => {
        try {
            await addLeadToWorkflow(token?.token || '', workspace?.workspace?.id || '', leadId, { workflowId: "default-workflow" }) // TODO: Add workflow selection
            toast.success("Lead added to workflow successfully!")
        } catch (error) {
            console.error("Failed to add to workflow:", error)
            toast.error("Failed to add to workflow")
        }
    }

    // Add to Database Dialog Component
    const AddToDatabaseDialog = () => {
        console.log("🔍 AddToDatabaseDialog render - open:", addToDatabaseDialogOpen)
        console.log("🔍 Available databases:", availableDatabases.length)
        console.log("🔍 Loading databases:", loadingDatabases)
        console.log("🔍 Selected lead ID:", selectedLeadIdForAction)
        console.log("🔍 Selected database ID:", selectedDatabaseId)
        console.log("🔍 Full availableDatabases array:", availableDatabases)
        
        return (
            <Dialog open={addToDatabaseDialogOpen} onOpenChange={setAddToDatabaseDialogOpen} key={`dialog-${dialogKey}`}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Add Lead to Database</DialogTitle>
                    </DialogHeader>
                <div className="py-4">
                    {/* Test button to manually open dialog */}
                    <div className="mb-4 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-xs text-yellow-800 mb-2">Debug Info:</p>
                        <p className="text-xs">Dialog Open: {addToDatabaseDialogOpen ? 'YES' : 'NO'}</p>
                        <p className="text-xs">Loading: {loadingDatabases ? 'YES' : 'NO'}</p>
                        <p className="text-xs">Databases: {availableDatabases.length}</p>
                        <p className="text-xs">Lead ID: {selectedLeadIdForAction || 'None'}</p>
                        <p className="text-xs">Dialog Key: {dialogKey}</p>
                        <button 
                            onClick={() => {
                                console.log("🔍 Manual dialog open test")
                                setAddToDatabaseDialogOpen(true)
                                setSelectedLeadIdForAction("test-lead-id")
                                setAvailableDatabases([{id: "test", name: "Test Database"}])
                                setDialogKey(prev => prev + 1)
                            }}
                            className="mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded"
                        >
                            Test Open Dialog
                        </button>
                    </div>
                    
                    {loadingDatabases ? (
                        <div className="text-center">Loading databases...</div>
                    ) : availableDatabases.length === 0 ? (
                        <div className="text-center text-muted-foreground">
                            <p>No databases available</p>
                            <p className="text-xs mt-2">You may need to create a database first in the Databases section.</p>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Select Database:</label>
                            <div className="space-y-2 max-h-48 overflow-y-auto">
                                {availableDatabases.map((database) => (
                                    <div
                                        key={database.id}
                                        className={`p-3 border rounded cursor-pointer transition-colors ${
                                            selectedDatabaseId === database.id 
                                                ? 'border-blue-500 bg-blue-50' 
                                                : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                        onClick={() => setSelectedDatabaseId(database.id)}
                                    >
                                        <div className="font-medium">{database.name}</div>
                                        {database.description && (
                                            <div className="text-sm text-muted-foreground">{database.description}</div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => setAddToDatabaseDialogOpen(false)}
                        disabled={addingToDatabase}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmAddToDatabase}
                        disabled={!selectedDatabaseId || addingToDatabase}
                    >
                        {addingToDatabase ? "Adding..." : "Add to Database"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        )
    }

    // Shared modals
    const SharedModals = () => (
        <>
            <UnlockEmailModal
                open={emailModalOpen}
                onOpenChange={setEmailModalOpen}
                leadId={selectedLeadId}
                onUnlockSuccess={(unlockedLead) => {
                    if (unlockEmailCallbackRef.current) {
                        unlockEmailCallbackRef.current(unlockedLead)
                    }
                }}
            />
            <UnlockPhoneModal
                open={phoneModalOpen}
                onOpenChange={setPhoneModalOpen}
                leadId={selectedLeadId}
                onUnlockSuccess={(unlockedLead) => {
                    if (unlockPhoneCallbackRef.current) {
                        unlockPhoneCallbackRef.current(unlockedLead)
                    }
                }}
            />
            <AddToDatabaseDialog />
        </>
    )

    return {
        // State
        selectedLeads,
        setSelectedLeads,
        emailModalOpen,
        phoneModalOpen,
        selectedLeadId,
        
        // Selection handlers
        handleSelectAll,
        handleSelectLead,
        
        // Unlock handlers
        handleUnlockEmail,
        handleUnlockPhone,
        
        // Navigation handlers
        handleNameClick,
        handleViewLinks,
        
        // Contact links
        getContactLinks,
        
        // Import handler
        handleImportLeads,
        
        // API conversion
        convertApiLeadsToUI,
        
        // Filter logic
        getFilteredLeads,
        
        // Lead actions
        handleSendEmail,
        handleAddToSegments,
        handleAddToDatabase,
        handleAddToWorkflow,
        
        // Callback setters
        setUnlockEmailCallback: (callback: (data: any) => void) => {
            unlockEmailCallbackRef.current = callback
        },
        setUnlockPhoneCallback: (callback: (data: any) => void) => {
            unlockPhoneCallbackRef.current = callback
        },
        
        // Components
        SharedModals,
        PeopleTableHeader
    }
}

// Keep old hook for backward compatibility
export const useLeadActions = () => {
    const leadManagement = useLeadManagement()
    return {
        handleSendEmail: leadManagement.handleSendEmail,
        handleAddToSegments: leadManagement.handleAddToSegments,
        handleAddToDatabase: leadManagement.handleAddToDatabase,
        handleAddToWorkflow: leadManagement.handleAddToWorkflow,
        AddToDatabaseDialog: leadManagement.SharedModals
    }
}

const People = ({ activeSubTab, onLeadCreated }: PeopleProps) => {
    const { workspace } = useWorkspace()
    const { token } = useAuth()
    
    // Shared sidebar state that persists across tab switches
    const [sidebarOpen, setSidebarOpen] = useState(false)
    
    // Centralized lead management and actions
    const leadManagement = useLeadManagement()
    const leadActions = useLeadActions()
    
    const sidebarState = {
        isOpen: sidebarOpen,
        setIsOpen: setSidebarOpen
    }
    
    // Shared props for all sub-components
    const sharedProps = {
        onLeadCreated,
        token: token?.token,
        workspaceId: workspace?.workspace?.id,
        // Pass shared components and actions
        ActionButton,
        ViewLinksModal,
        LeadActionsDropdown,
        leadActions,
        leadManagement
    }

    // Render the appropriate component based on the active secondary tab
    const renderContent = () => {
        switch (activeSubTab) {
            case 'my-leads':
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
            case 'find-leads':
                return (
                    <>
                        <FindLeads {...sharedProps} sidebarState={sidebarState} />
                        <leadManagement.SharedModals />
                    </>
                )
            case 'saved-search':
                return (
                    <>
                        <SavedSearch {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
            default:
                return (
                    <>
                        <MyLeads {...sharedProps} />
                        <leadManagement.SharedModals />
                    </>
                )
        }
    }

    return renderContent()
}

export default People