export interface Lead {
    id: string;
    workspaceId: string;
    apolloId?: string;
    type: 'person' | 'company';
    source: 'apollo' | 'manual' | 'import';
    name: string;
    email?: string;
    companyDomain?: string;
    isUnlocked: boolean;
    lastEnrichedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    createdById?: string;
    updatedById?: string;
    normalizedData: NormalizedLeadData;
    apolloData?: ApolloPersonData | ApolloCompanyData;
    searchHashes?: string[];
    meta?: LeadMeta;
}

// Updated to match actual Apollo API response structure from PEOPLE_DATA_STRUCTURE.md
export interface ApolloPersonData {
    id: string;
    first_name?: string;
    last_name?: string;
    name?: string;
    linkedin_url?: string;
    title?: string;
    email_status?: string;
    photo_url?: string;
    twitter_url?: string | null;
    github_url?: string | null;
    facebook_url?: string | null;
    extrapolated_email_confidence?: number | null;
    headline?: string;
    email?: string;
    organization_id?: string;
    employment_history?: Array<{
        _id?: string;
        created_at?: string | null;
        current?: boolean;
        degree?: string | null;
        description?: string | null;
        emails?: string[] | null;
        end_date?: string | null;
        grade_level?: string | null;
        kind?: string | null;
        major?: string | null;
        organization_id?: string | null;
        organization_name?: string;
        raw_address?: string | null;
        start_date?: string;
        updated_at?: string | null;
        id?: string;
        key?: string;
    }>;
    street_address?: string;
    city?: string;
    state?: string;
    country?: string;
    postal_code?: string | null;
    formatted_address?: string;
    time_zone?: string;
    organization?: {
        id?: string;
        name?: string;
        website_url?: string | null;
        blog_url?: string | null;
        angellist_url?: string | null;
        linkedin_url?: string | null;
        twitter_url?: string | null;
        facebook_url?: string | null;
        primary_phone?: {
            number?: string;
            source?: string;
            sanitized_number?: string;
        } | Record<string, never>;
        languages?: string[];
        alexa_ranking?: number | null;
        phone?: string | null;
        linkedin_uid?: string;
        founded_year?: number | null;
        publicly_traded_symbol?: string | null;
        publicly_traded_exchange?: string | null;
        logo_url?: string | null;
        crunchbase_url?: string | null;
        primary_domain?: string | null;
        sanitized_phone?: string | null;
        organization_headcount_six_month_growth?: number | null;
        organization_headcount_twelve_month_growth?: number | null;
        organization_headcount_twenty_four_month_growth?: number | null;
    };
    departments?: string[];
    subdepartments?: string[];
    seniority?: string;
    functions?: string[];
    intent_strength?: string | null;
    show_intent?: boolean;
    email_domain_catchall?: boolean;
    revealed_for_current_team?: boolean;
}

// Updated to match actual Apollo API response structure from COMPANY_DATA_STRUCTURE.md
export interface ApolloCompanyData {
    id: string;
    name: string;
    phone?: string | null;
    blog_url?: string | null;
    logo_url?: string | null;
    languages?: string[];
    show_intent?: boolean;
    twitter_url?: string | null;
    website_url?: string | null;
    facebook_url?: string | null;
    founded_year?: number | null;
    linkedin_uid?: string | null;
    linkedin_url?: string | null;
    alexa_ranking?: number | null;
    angellist_url?: string | null;
    primary_phone?: {
        number?: string;
        source?: string;
        sanitized_number?: string;
    } | null;
    crunchbase_url?: string | null;
    primary_domain?: string | null;
    intent_strength?: string | null;
    sanitized_phone?: string | null;
    organization_revenue?: number | null;
    intent_signal_account?: string | null;
    publicly_traded_symbol?: string | null;
    owned_by_organization_id?: string | null;
    publicly_traded_exchange?: string | null;
    has_intent_signal_account?: boolean | null;
    organization_revenue_printed?: string | null;
    organization_headcount_six_month_growth?: number | null;
    organization_headcount_twelve_month_growth?: number | null;
    organization_headcount_twenty_four_month_growth?: number | null;
    owned_by_organization?: {
        id?: string;
        name?: string;
        website_url?: string;
    } | null;
    // Additional fields that might exist
    industry?: string;
    keywords?: string[];
    estimated_num_employees?: number;
    retail_location_count?: number;
    stage?: string;
    short_description?: string;
    technologies?: string[];
    annual_revenue?: string;
    total_funding?: number;
    latest_funding_round_date?: string;
    latest_funding_stage?: string;
    seo_description?: string;
    technology_names?: string[];
    current_technologies?: Array<{
        name?: string;
        category?: string;
        description?: string;
    }>;
    account_id?: string;

}

export interface NormalizedLeadData {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    jobTitle?: string;
    company?: string;
    companyDomain?: string;
    linkedinUrl?: string;
    photoUrl?: string;
    location?: {
        country?: string;
        state?: string;
        city?: string;
    };
    isEmailVisible: boolean;
    isPhoneVisible: boolean;
    confidence?: number;
}

export interface LeadVoteFeedback {
    vote: 'up' | 'down';
    feedback: string;
    feedbackType: 'predefined' | 'custom';
    createdAt: Date;
    createdBy: string;
}

export interface LeadMeta {
    votes?: LeadVoteFeedback[];
    // Additional metadata fields can be added here as needed
}

export interface PersonFilters {
    jobTitles?: string[];
    managementLevel?: string[];
    jobFunction?: string[];
    location?: string[];
    seniority?: string[];
    departments?: string[];
    skills?: string[];
}

export interface CompanyFilters {
    industry?: string[];
    industries?: string[]; // Add support for both field names
    companySize?: string[];
    companyType?: string[];
    location?: string[];
    technologies?: string[];
    keywords?: string[];
    revenue?: {
        min?: number;
        max?: number;
    };
    employees?: {
        min?: number;
        max?: number;
    };
    foundedYear?: {
        min?: number;
        max?: number;
    };
}

export interface SignalFilters {
    recentlyPromoted?: boolean;
    formerChampionChangedJobs?: boolean;
    highBuyingIntent?: boolean;
    rapidGrowth?: boolean;
    openedEmails?: boolean;
    newRole?: boolean;
    jobChanges?: boolean;
    companyGrowth?: boolean;
    newTechnologies?: boolean;
    fundingEvents?: boolean;
}

export interface SearchFilters {
    person?: PersonFilters;
    company?: CompanyFilters;
    signals?: SignalFilters;
    customFilters?: Record<string, string | number | boolean | string[] | number[]>;
}

export interface SearchPagination {
    page: number;
    limit: number;
    totalCount?: number;
    hasNextPage?: boolean;
}

export interface SearchResultMetadata {
    apolloRequestId?: string;
    apolloCreditsUsed?: number;
    processingTimeMs?: number;
    resultQuality?: 'high' | 'medium' | 'low';
    dataFreshness?: Date;
    totalPagesAvailable?: number; // Track how many pages are currently cached
}

export interface SearchLeadsRequest {
    filters: SearchFilters;
    pagination?: SearchPagination;
    excludeMyLeads?: boolean;
    customFilters?: Record<string, string | number | boolean | string[] | number[]>;
}

export interface SearchLeadsResponse {
    leads: Lead[];
    totalCount: number;
    hasNextPage: boolean;
    searchId: string;
    filters: SearchFilters;
    metadata: SearchResultMetadata;
}

export interface UnlockLeadResponse {
    lead: Lead;
    unlock: {
        id: string;
        unlockedAt: Date;
        unlockType: string;
        creditsUsed: number;
        isSuccessful: boolean;
    };
    alreadyUnlocked: boolean;
}

export interface SavedSearch {
    id: string;
    workspaceId: string;
    name: string;
    description?: string;
    filters: SearchFilters;
    searchHash: string;
    totalCount: number;
    numberLoaded: number;
    pageLoaded: number;
    resultIds: string[];
    isSaved: boolean;
    searchType: 'people' | 'company';
    lastExecutedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    expiresAt?: Date;
    isActive: boolean;
    createdById: string;
    updatedById?: string;
    metadata?: SearchResultMetadata;
}

export interface VoteFeedbackOptions {
    upvote: string[];
    downvote: string[];
}