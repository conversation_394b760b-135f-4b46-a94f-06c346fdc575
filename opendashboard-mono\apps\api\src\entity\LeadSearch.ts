import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"

export interface PersonFilters {
    jobTitles?: string[];
    managementLevel?: string[];
    jobFunction?: string[];
    location?: string[];
    seniority?: string[];
    departments?: string[];
    skills?: string[];
}

export interface CompanyFilters {
    industry?: string[];
    industries?: string[]; // Add support for both field names
    companySize?: string[];
    companyType?: string[];
    location?: string[];
    technologies?: string[];
    keywords?: string[];
    revenue?: {
        min?: number;
        max?: number;
    };
    employees?: {
        min?: number;
        max?: number;
    };
    foundedYear?: {
        min?: number;
        max?: number;
    };
}

export interface SignalFilters {
    recentlyPromoted?: boolean;
    formerChampionChangedJobs?: boolean;
    highBuyingIntent?: boolean;
    rapidGrowth?: boolean;
    openedEmails?: boolean;
    newRole?: boolean;
    jobChanges?: boolean;
    companyGrowth?: boolean;
    newTechnologies?: boolean;
    fundingEvents?: boolean;
}

export interface SearchFilters {
    person?: PersonFilters;
    company?: CompanyFilters;
    signals?: SignalFilters;
    excludeMyLeads?: boolean;
    customFilters?: Record<string, string | number | boolean | string[] | number[]>;
}

export interface SearchPagination {
    page: number;
    limit: number;
    totalCount?: number;
    hasNextPage?: boolean;
}

export interface SearchResultMetadata {
    apolloRequestId?: string;
    apolloCreditsUsed?: number;
    processingTimeMs?: number;
    resultQuality?: 'high' | 'medium' | 'low';
    dataFreshness?: Date;
    apolloPage?: number; // Track which Apollo page we're on
    apolloTotalPages?: number; // Track total Apollo pages available
    totalPagesAvailable?: number; // Track how many pages are currently cached
}

@Entity()
@Index(["workspaceId", "searchHash"], { unique: true })
@Index(["workspaceId", "createdAt"])
@Index(["workspaceId", "name"])
export class LeadSearch {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string;

    @Column({type: 'varchar', nullable: true})
    name: string;

    @Column({type: 'text', nullable: true})
    description: string;

    @Index()
    @Column({type: 'varchar', nullable: false, unique: false})
    searchHash: string;

    @Column({type: "json", nullable: false})
    filters: SearchFilters;

    @Column({type: "json", nullable: false})
    pagination: SearchPagination;

    @Column({type: "json", nullable: true})
    resultIds: string[];

    @Index()
    @Column({default: 0, type: 'int'})
    totalCount: number;

    @Index()
    @Column({default: 0, type: 'int'})
    numberLoaded: number;

    @Index()
    @Column({default: 1, type: 'int'})
    pageLoaded: number;

    @Column({type: "json", nullable: true})
    metadata: SearchResultMetadata;

    @Index()
    @Column({default: 1, type: 'int'})
    apolloPage: number; // Track which Apollo page we're on (1, 2, 3, etc.)

    @Index()
    @Column({default: false, type: 'boolean'})
    isSaved: boolean;

    @Index()
    @Column({default: true, type: 'boolean'})
    isActive: boolean;

    @Index()
    @Column({type: 'varchar', nullable: false, default: 'people'})
    searchType: 'people' | 'company';

    @Index()
    @Column({type: 'timestamp', nullable: true})
    lastExecutedAt: Date;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    expiresAt: Date;

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    updatedById: string;

    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

    @Column({type: "json", nullable: true})
    meta: Record<string, string | number | boolean | string[] | number[]>;
}
