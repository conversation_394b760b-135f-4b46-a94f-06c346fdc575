import { getRepository } from '../connection/db';
import { BaseService } from './service';
import { Repository } from 'typeorm/repository/Repository';
import { ServerProcessingError, UniqueConstraintError } from "../errors/AppError";
import { LeadSearch, SearchFilters, SearchPagination, SearchResultMetadata } from "../entity/LeadSearch";
import { MoreThan } from "typeorm";

export interface CreateLeadSearchData extends Pick<LeadSearch, 'workspaceId' | 'searchHash' | 'filters' | 'pagination' | 'resultIds' | 'totalCount' | 'numberLoaded' | 'pageLoaded' | 'createdById'> {
    name?: string;
    description?: string;
    metadata?: SearchResultMetadata;
    isSaved?: boolean;
    isActive?: boolean;
    expiresAt?: Date;
    apolloPage?: number; // Add apolloPage field
    lastExecutedAt?: Date; // Add lastExecutedAt field
}

export interface UpdateLeadSearchData extends Partial<Pick<LeadSearch, 'name' | 'description' | 'resultIds' | 'totalCount' | 'numberLoaded' | 'pageLoaded' | 'lastExecutedAt' | 'updatedById'>> {
    metadata?: SearchResultMetadata;
}

export class LeadSearchService extends BaseService<LeadSearch> {

    initRepository = (): Repository<LeadSearch> => {
        return getRepository(LeadSearch);
    }

    createSearch = async (data: CreateLeadSearchData): Promise<LeadSearch> => {
        try {
            // Set default apolloPage if not provided
            const searchData = {
                ...data,
                apolloPage: data.apolloPage || 1
            };
            
            const result = await this.insert(searchData);
            return result;
        } catch (err) {
            throw new ServerProcessingError(err.message);
        }
    }

    findByWorkspaceAndHash = async (workspaceId: string, searchHash: string): Promise<LeadSearch | null> => {
        // Use raw query builder to see exactly what's happening
        const queryBuilder = this.getRepository()
            .createQueryBuilder('search')
            .where('search.workspaceId = :workspaceId', { workspaceId })
            .andWhere('search.searchHash = :searchHash', { searchHash })
            .andWhere('search.isActive = :isActive', { isActive: true });
        
        
        const result = await queryBuilder.getOne();

        
        return result;
    }

    findRecentValidSearch = async (workspaceId: string, searchHash: string, maxAgeMinutes: number = 60): Promise<LeadSearch | null> => {
        const cutoffTime = new Date(Date.now() - (maxAgeMinutes * 60 * 1000));
        
        return await this.findOne({ 
            workspaceId, 
            searchHash,
            isActive: true,
            lastExecutedAt: MoreThan(cutoffTime)
        });
    }

    updateSearchResults = async (searchId: string, resultData: {
  resultIds: string[];
        totalCount: number;
        numberLoaded: number;
        pageLoaded: number;
        metadata?: SearchResultMetadata;
    }): Promise<void> => {
        const updateData: UpdateLeadSearchData = {
            ...resultData,
            lastExecutedAt: new Date()
        };
        
        await this.update({ id: searchId }, updateData);
    }

    getSavedSearches = async (workspaceId: string, options: {
        page?: number;
        limit?: number;
        search?: string;
        searchType?: 'people' | 'company';
    } = {}): Promise<{ searches: LeadSearch[], total: number }> => {
        console.log('🔍 [LEAD SEARCH SERVICE] 🚀 Starting getSavedSearches')
        console.log('🔍 [LEAD SEARCH SERVICE] 📊 Parameters:', {
            workspaceId,
            page: options.page,
            limit: options.limit,
            search: options.search,
            searchType: options.searchType
        })
        
        const { page = 1, limit = 20, search, searchType } = options;
        const offset = (page - 1) * limit;

        const queryBuilder = this.getRepository()
            .createQueryBuilder('leadSearch')
            .where('leadSearch.workspaceId = :workspaceId', { workspaceId })
            .andWhere('leadSearch.isSaved = :isSaved', { isSaved: true });

        if (searchType) {
            queryBuilder.andWhere('leadSearch.searchType = :searchType', { searchType });
        }

        if (search) {
            queryBuilder.andWhere(
                '(leadSearch.name LIKE :search OR leadSearch.description LIKE :search)',
                { search: `%${search}%` }
            );
        }

        console.log('🔍 [LEAD SEARCH SERVICE] 🔍 Executing count query...')
        const total = await queryBuilder.getCount();
        console.log('🔍 [LEAD SEARCH SERVICE] 📊 Total count:', total)
        
        console.log('🔍 [LEAD SEARCH SERVICE] 🔍 Executing search query...')
        const searches = await queryBuilder
            .orderBy('leadSearch.createdAt', 'DESC')
            .skip(offset)
            .take(limit)
            .getMany();

        console.log('🔍 [LEAD SEARCH SERVICE] 📋 Found searches:', searches.length)
        if (searches.length > 0) {
            console.log('🔍 [LEAD SEARCH SERVICE] 📝 First search:', {
                id: searches[0].id,
                name: searches[0].name,
                searchType: searches[0].searchType,
                isSaved: searches[0].isSaved,
                createdAt: searches[0].createdAt
            })
        }

        return { searches, total };
    }

    saveSearch = async (searchId: string, name: string, description?: string): Promise<void> => {
        await this.update({ id: searchId }, {
            name,
            description,
            isSaved: true
        });
    }

    markSearchAsSaved = async (workspaceId: string, searchHash: string, name: string, description?: string, userId?: string): Promise<LeadSearch> => {
        const search = await this.findByWorkspaceAndHash(workspaceId, searchHash);
        
        if (search) {
            await this.update({ id: search.id }, {
                name,
                description,
                isSaved: true,
                updatedById: userId
            });
            return { ...search, name, description, isSaved: true };
        }
        
        throw new Error('Search not found');
    }

    deleteSearch = async (workspaceId: string, searchId: string): Promise<void> => {
        await this.update({ id: searchId, workspaceId }, { isActive: false });
    }

    cleanupOldSearches = async (maxAgeHours: number = 24): Promise<void> => {
        const cutoffTime = new Date(Date.now() - (maxAgeHours * 60 * 60 * 1000));
        
        await this.getRepository()
            .createQueryBuilder()
            .update(LeadSearch)
            .set({ isActive: false })
            .where('isSaved = :isSaved', { isSaved: false })
            .andWhere('createdAt < :cutoffTime', { cutoffTime })
            .execute();
    }

   

    findByWorkspaceAndSearchId = async (workspaceId: string, searchId: string): Promise<LeadSearch | null> => {
        return await this.findOne({ 
            workspaceId, 
            id: searchId
        });
    }

    updateSearch = async (searchId: string, updateData: UpdateLeadSearchData): Promise<boolean> => {
        return await this.update({ id: searchId }, updateData);
    }

    removeSearch = async (searchId: string): Promise<boolean> => {
        return await this.hardRemove({ id: searchId });
    }

    findRecentSearches = async (workspaceId: string, limit: number = 10): Promise<LeadSearch[]> => {
        return await this.find(
            { workspaceId, isActive: true },
            { lastExecutedAt: 'DESC' },
            limit
        );
    }

    // Fix corrupted search records that have null lastExecutedAt
    fixCorruptedSearches = async (workspaceId: string): Promise<number> => {

        
        const corruptedSearches = await this.getRepository()
            .createQueryBuilder('search')
            .where('search.workspaceId = :workspaceId', { workspaceId })
            .andWhere('search.isActive = :isActive', { isActive: true })
            .andWhere('search.lastExecutedAt IS NULL')
            .andWhere('search.resultIds IS NOT NULL')
            .andWhere('search.resultIds != :emptyArray', { emptyArray: '[]' })
            .getMany();
        
        let fixedCount = 0;
        for (const search of corruptedSearches) {
            try {
                await this.update({ id: search.id }, {
                    lastExecutedAt: search.createdAt || new Date()
                });
                fixedCount++;
            } catch (error) {
            }
        }
        
        return fixedCount;
    }

    findByWorkspaceAndFilters = async (workspaceId: string, filters: SearchFilters): Promise<LeadSearch[]> => {
        const queryBuilder = this.getRepository()
            .createQueryBuilder('search')
            .where('search.workspaceId = :workspaceId', { workspaceId })
            .andWhere('search.isActive = :isActive', { isActive: true });

        // Apply search filters if they exist
        if (filters && Object.keys(filters).length > 0) {
            // This would need to be implemented based on how filters are stored
            // For now, return all active searches
        }

        return await queryBuilder
            .orderBy('search.lastExecutedAt', 'DESC')
            .getMany();
    }
}
