import { NextFunction, Request, Response } from "express";
import { AuthInfo, getAuthInfo } from "../../businessLogic/authInfo";
import { 
    SearchPeopleLeads, 
    SearchCompanyLeads,
    GetMyLeads, 
    GetMyCompanyLeads,
    UnlockLead, 
    SaveSearch, 
    GetSavedSearches,
    GetSavedSearch,
    UpdateSavedSearch,
    DeleteSavedSearch,
    ExecuteSavedSearch,
    GetLead,
    UpdateLead,
    DeleteLead,
    VoteLead,
    BulkUnlockLeads,
    ExportLeads,
    GetVoteFeedbackOptions,
    AddLeadToSegment,
    AddLeadToWorkflow,
    AddLeadToDatabase,
    SendEmailToLead,
    GetLeadsBySearchHashes,
    IsLeadUnlocked,
    CreateLeadInApollo,
    BulkEnrichLeads,
    SearchCompanyLeadsWithBulkEnrichment,
    SearchPeopleLeadsWithBulkEnrichment
} from "../../businessLogic/lead";
import { ErrorMessage, NotfoundError, RequiredParameterError, BadRequestError } from "../../errors/AppError";
import { ApiResponseStatus, GenericApiResponseBody, ApiMessage } from "../interface";
import { UnlockSource } from "../../entity/LeadUnlock";

export class LeadController {
    
    async searchPeopleLeads(request: Request, response: Response, next: NextFunction) {
console.log(`🔍 [CONTROLLER DEBUG] searchPeopleLeads called`);
console.log(`🔍 [CONTROLLER DEBUG] Request body:`, JSON.stringify(request.body, null, 2));
console.log(`🔍 [CONTROLLER DEBUG] Request params:`, JSON.stringify(request.params, null, 2));
        
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { filters, pagination } = request.body;

        if (!filters) {
            throw new RequiredParameterError('filters');
        }

        // Extract excludeMyLeads from the filters object where the frontend actually sends it
        const excludeMyLeads = filters.excludeMyLeads || false;

        console.log(`🔍 [CONTROLLER DEBUG] Extracted excludeMyLeads from filters:`, {
            filtersExcludeMyLeads: filters.excludeMyLeads,
            extractedExcludeMyLeads: excludeMyLeads
        });

        console.log(`🔍 [CONTROLLER DEBUG] Calling SearchPeopleLeads with:`, {
            userId: authInfo.userId,
            workspaceId,
            filters: JSON.stringify(filters),
            pagination: pagination || { page: 1, limit: 50 },
            excludeMyLeads: excludeMyLeads
        });

        const result = await SearchPeopleLeads(authInfo.userId, workspaceId, {
            filters,
            pagination: pagination || { page: 1, limit: 50 },
            excludeMyLeads: excludeMyLeads
        });

        console.log(`🔍 [CONTROLLER DEBUG] SearchPeopleLeads returned:`, {
            leadsCount: result.leads.length,
            totalCount: result.totalCount,
            hasNextPage: result.hasNextPage,
            searchId: result.searchId
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async searchCompanyLeads(request: Request, response: Response, next: NextFunction) {
console.log(`🔍 [CONTROLLER DEBUG] searchCompanyLeads called`);
console.log(`🔍 [CONTROLLER DEBUG] Request body:`, JSON.stringify(request.body, null, 2));
console.log(`🔍 [CONTROLLER DEBUG] Request params:`, JSON.stringify(request.params, null, 2));
        
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { filters, pagination } = request.body;

        if (!filters) {
            throw new RequiredParameterError('filters');
        }

        // Extract excludeMyLeads from the filters object where the frontend actually sends it
        const excludeMyLeads = filters.excludeMyLeads || false;

        console.log(`🔍 [CONTROLLER DEBUG] Extracted excludeMyLeads from filters:`, {
            filtersExcludeMyLeads: filters.excludeMyLeads,
            extractedExcludeMyLeads: excludeMyLeads
        });

        console.log(`🔍 [CONTROLLER DEBUG] Calling SearchCompanyLeads with:`, {
            userId: authInfo.userId,
            workspaceId,
            filters: JSON.stringify(filters),
            pagination: pagination || { page: 1, limit: 50 },
            excludeMyLeads: excludeMyLeads
        });

        const result = await SearchCompanyLeads(authInfo.userId, workspaceId, {
            filters,
            pagination: pagination || { page: 1, limit: 50 },
            excludeMyLeads: excludeMyLeads
        });

        console.log(`🔍 [CONTROLLER DEBUG] SearchCompanyLeads returned:`, {
            leadsCount: result.leads.length,
            totalCount: result.totalCount,
            hasNextPage: result.hasNextPage,
            searchId: result.searchId
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async getMyLeads(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { page = 1, limit = 50, search, filters } = request.query;

        const result = await GetMyLeads(authInfo.userId, workspaceId, {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            search: search as string,
            filters: filters ? JSON.parse(filters as string) : undefined
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async getMyCompanyLeads(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { page = 1, limit = 50, search, filters } = request.query;

        const result = await GetMyCompanyLeads(authInfo.userId, workspaceId, {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            search: search as string,
            filters: filters ? JSON.parse(filters as string) : undefined
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async unlockLead(request: Request, response: Response, next: NextFunction) {
        console.log('🔍 [CONTROLLER] ==========================================');
        console.log('🔍 [CONTROLLER] 🎮 UNLOCK LEAD CONTROLLER CALLED');
        console.log('🔍 [CONTROLLER] ==========================================');
        
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { unlockType = 'full' } = request.body;
        
        console.log('🔍 [CONTROLLER] Request Parameters:', { workspaceId, leadId });
        console.log('🔍 [CONTROLLER] Request Body:', { unlockType });
        console.log('🔍 [CONTROLLER] Auth Info:', { userId: authInfo.userId });

        const result = await UnlockLead(authInfo.userId, workspaceId, leadId, {
            unlockType,
            source: UnlockSource.Manual
        });

        console.log('🔍 [CONTROLLER] 📊 BUSINESS LOGIC RESULT:');
        console.log('🔍 [CONTROLLER] - Result Type:', typeof result);
        console.log('🔍 [CONTROLLER] - Has Lead:', !!result.lead);
        console.log('🔍 [CONTROLLER] - Has Unlock:', !!result.unlock);
        console.log('🔍 [CONTROLLER] - Already Unlocked:', result.alreadyUnlocked);
        
        if (result.lead) {
            console.log('🔍 [CONTROLLER] - Lead ID:', result.lead.id);
            console.log('🔍 [CONTROLLER] - Lead Is Unlocked:', result.lead.isUnlocked);
            console.log('🔍 [CONTROLLER] - Lead Apollo Data Keys:', Object.keys(result.lead.apolloData || {}));
        }
        
        if (result.unlock) {
            console.log('🔍 [CONTROLLER] - Unlock ID:', result.unlock.id);
            console.log('🔍 [CONTROLLER] - Unlock Type:', result.unlock.unlockType);
        }

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        console.log('🔍 [CONTROLLER] 📤 SENDING RESPONSE:');
        console.log('🔍 [CONTROLLER] - Response Status:', responseData.status);
        console.log('🔍 [CONTROLLER] - Response Message:', responseData.message);
        console.log('🔍 [CONTROLLER] - Response Data Keys:', Object.keys(responseData.data));
        console.log('🔍 [CONTROLLER] ==========================================');
        
        return response.json(responseData);
    }

    async saveSearch(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        console.log('💾 [CONTROLLER SAVE SEARCH] 🚀 Received save search request')
        console.log('💾 [CONTROLLER SAVE SEARCH] 📊 Request body:', request.body)

        const { name, description, filters, searchId, searchType } = request.body;
        
        console.log('💾 [CONTROLLER SAVE SEARCH] 📋 Extracted data:', {
            name,
            description,
            hasFilters: !!filters,
            searchId,
            searchType
        })

        if (!name || !filters) {
            throw new RequiredParameterError('name and filters are required');
        }

        const result = await SaveSearch(authInfo.userId, workspaceId, {
            name,
            description,
            filters,
            searchId,
            searchType
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async getSavedSearches(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { page = 1, limit = 20, search, searchType } = request.query;

        const result = await GetSavedSearches(authInfo.userId, workspaceId, {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            search: search as string,
            searchType: searchType as 'people' | 'company'
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async getSavedSearch(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, searchId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const result = await GetSavedSearch(authInfo.userId, workspaceId, searchId);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async updateSavedSearch(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, searchId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const updateData = request.body;

        const result = await UpdateSavedSearch(authInfo.userId, workspaceId, searchId, updateData);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async deleteSavedSearch(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, searchId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        await DeleteSavedSearch(authInfo.userId, workspaceId, searchId);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: { success: true }
        };
        
        return response.json(responseData);
    }

    async executeSavedSearch(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, searchId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { pagination, searchType } = request.body;

        const result = await ExecuteSavedSearch(authInfo.userId, workspaceId, searchId, {
            pagination: pagination || { page: 1, limit: 50 },
            searchType: searchType || 'people'
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async getLead(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const result = await GetLead(authInfo.userId, workspaceId, leadId);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async updateLead(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const updateData = request.body;

        const result = await UpdateLead(authInfo.userId, workspaceId, leadId, updateData);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async deleteLead(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        await DeleteLead(authInfo.userId, workspaceId, leadId);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: { success: true }
        };
        
        return response.json(responseData);
    }

    async voteLead(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { vote, feedback, feedbackType = 'custom' } = request.body;

        if (!vote || !['up', 'down'].includes(vote)) {
            throw new RequiredParameterError('Valid vote (up/down) is required');
        }

        if (!feedback || feedback.trim().length === 0) {
            throw new RequiredParameterError('Feedback is required for voting');
        }

        const result = await VoteLead(authInfo.userId, workspaceId, leadId, {
            vote,
            feedback,
            feedbackType
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async bulkUnlockLeads(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { leadIds, unlockType = 'full' } = request.body;

        if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
            throw new RequiredParameterError('leadIds array is required');
        }

        const result = await BulkUnlockLeads(authInfo.userId, workspaceId, leadIds, {
            unlockType,
            source: UnlockSource.Bulk
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async exportLeads(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { leadIds, format = 'csv', includeFullData = false } = request.body;

        if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
            throw new RequiredParameterError('leadIds array is required');
        }

        const result = await ExportLeads(authInfo.userId, workspaceId, leadIds, {
            format,
            includeFullData
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async getVoteFeedbackOptions(request: Request, response: Response, next: NextFunction) {
        const result = GetVoteFeedbackOptions();

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

 
    async addLeadToSegment(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { name, databaseId } = request.body;

        if (!name) {
            throw new RequiredParameterError('Segment name is required');
        }

        const result = await AddLeadToSegment(authInfo.userId, workspaceId, leadId, {
            name,
            databaseId
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async addLeadToWorkflow(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { workflowId } = request.body;

        if (!workflowId) {
            throw new RequiredParameterError('Workflow ID is required');
        }

        const result = await AddLeadToWorkflow(authInfo.userId, workspaceId, leadId, { workflowId });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async addLeadToDatabase(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { targetDatabaseId } = request.body;

        if (!targetDatabaseId) {
            throw new RequiredParameterError('Target database ID is required');
        }

        const result = await AddLeadToDatabase(authInfo.userId, workspaceId, leadId, { targetDatabaseId });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async sendEmailToLead(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId, leadId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { subject, body, template } = request.body;

        if (!subject || !body) {
            throw new RequiredParameterError('Subject and body are required');
        }

        const result = await SendEmailToLead(authInfo.userId, workspaceId, leadId, {
            subject,
            body,
            template
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }


    async createLeadInApollo(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);
        const leadData = request.body;


        if (!leadData.firstName || !leadData.lastName || !leadData.email) {
            throw new RequiredParameterError('firstName, lastName, and email are required');
        }

        const result = await CreateLeadInApollo(authInfo.userId, workspaceId, leadData);
        
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }


    async bulkEnrichLeads(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);
        const { leadIds, enrichmentType = 'full' } = request.body;


        if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
            throw new RequiredParameterError('leadIds array is required');
        }

        const result = await BulkEnrichLeads(authInfo.userId, workspaceId, { leadIds, enrichmentType });
        
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }


    async searchCompanyLeadsWithBulkEnrichment(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { filters, pagination } = request.body;

        if (!filters) {
            throw new RequiredParameterError('filters');
        }

        // Extract excludeMyLeads from the filters object where the frontend actually sends it
        const excludeMyLeads = filters.excludeMyLeads || false;

        console.log(`🔍 [CONTROLLER DEBUG] Extracted excludeMyLeads from filters:`, {
            filtersExcludeMyLeads: filters.excludeMyLeads,
            extractedExcludeMyLeads: excludeMyLeads
        });

        const result = await SearchCompanyLeadsWithBulkEnrichment(authInfo.userId, workspaceId, {
            filters,
            pagination: pagination || { page: 1, limit: 50 },
            excludeMyLeads: excludeMyLeads
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }

    async searchPeopleLeadsWithBulkEnrichment(request: Request, response: Response, next: NextFunction) {
        const { id: workspaceId } = request.params;
        const authInfo: AuthInfo = getAuthInfo(request);

        const { filters, pagination } = request.body;

        if (!filters) {
            throw new RequiredParameterError('filters');
        }

        // Extract excludeMyLeads from the filters object where the frontend actually sends it
        const excludeMyLeads = filters.excludeMyLeads || false;

        console.log(`🔍 [CONTROLLER DEBUG] Extracted excludeMyLeads from filters:`, {
            filtersExcludeMyLeads: filters.excludeMyLeads,
            extractedExcludeMyLeads: excludeMyLeads
        });

        const result = await SearchPeopleLeadsWithBulkEnrichment(authInfo.userId, workspaceId, {
            filters,
            pagination: pagination || { page: 1, limit: 50 },
            excludeMyLeads: excludeMyLeads
        });

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: result
        };
        
        return response.json(responseData);
    }
}