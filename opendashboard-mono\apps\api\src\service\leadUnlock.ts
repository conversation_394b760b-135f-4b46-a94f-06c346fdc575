import { getRepository } from '../connection/db';
import { BaseService } from './service';
import { Repository } from 'typeorm/repository/Repository';
import { ServerProcessingError, UniqueConstraintError } from "../errors/AppError";
import { LeadUnlock, UnlockType, UnlockSource, UnlockMetadata } from "../entity/LeadUnlock";
import { In } from "typeorm";

export interface CreateLeadUnlockData extends Pick<LeadUnlock, 'workspaceId' | 'leadId' | 'unlockedBy' | 'unlockType' | 'unlockSource' | 'unlockedAt'> {
    apolloData?: any;
    enrichedData?: any;
    metadata?: UnlockMetadata;
    creditsUsed?: number;
    isSuccessful?: boolean;
    failureReason?: string;
    expiresAt?: Date;
}

export interface UnlockSummary {
    leadId: string;
    unlockedAt: Date;
    unlockType: UnlockType;
    creditsUsed: number;
    isSuccessful: boolean;
}

export class LeadUnlockService extends BaseService<LeadUnlock> {

    initRepository = (): Repository<LeadUnlock> => {
        return getRepository(LeadUnlock);
    }

    createUnlock = async (data: CreateLeadUnlockData): Promise<LeadUnlock> => {
        try {
            const unlockData = {
                ...data,
                creditsUsed: data.creditsUsed || 1,
                isSuccessful: data.isSuccessful !== false, // Default to true unless explicitly false
                unlockedAt: data.unlockedAt || new Date()
            };
            
            return await this.insert(unlockData);
        } catch (err) {
            if (err.message.includes("Duplicate entry")) {
                throw new UniqueConstraintError('lead already unlocked by this user');
            }
            throw new ServerProcessingError(err.message);
        }
    }

    findExistingUnlock = async (workspaceId: string, leadId: string, unlockedBy: string): Promise<LeadUnlock | null> => {
        return await this.findOne({ 
            workspaceId, 
            leadId, 
            unlockedBy 
        });
    }

    getUnlockedLeadsForUser = async (workspaceId: string, userId: string): Promise<string[]> => {
        console.log('🔍 [BACKEND UNLOCK SERVICE] ==========================================');
        console.log('🔍 [BACKEND UNLOCK SERVICE] 📋 Getting unlocked leads for user');
        console.log('🔍 [BACKEND UNLOCK SERVICE] ==========================================');
        console.log('🔍 [BACKEND UNLOCK SERVICE] Workspace ID:', workspaceId);
        console.log('🔍 [BACKEND UNLOCK SERVICE] User ID:', userId);
        
        const unlocks = await this.find({ 
            workspaceId, 
            unlockedBy: userId,
            isSuccessful: true 
        });
        
        console.log('🔍 [BACKEND UNLOCK SERVICE] 📋 Found unlocks count:', unlocks.length);
        console.log('🔍 [BACKEND UNLOCK SERVICE] 📋 Unlock records:', unlocks.map(u => ({
            leadId: u.leadId,
            unlockType: u.unlockType,
            isSuccessful: u.isSuccessful,
            unlockedAt: u.unlockedAt
        })));
        
        const leadIds = unlocks.map(unlock => unlock.leadId);
        console.log('🔍 [BACKEND UNLOCK SERVICE] 📋 Returning lead IDs:', leadIds);
        console.log('🔍 [BACKEND UNLOCK SERVICE] ==========================================');
        
        return leadIds;
    }

    getUnlockHistory = async (workspaceId: string, leadId: string): Promise<LeadUnlock[]> => {
        return await this.find(
            { workspaceId, leadId },
            { unlockedAt: 'DESC' }
        );
    }

    getUserUnlockSummary = async (workspaceId: string, userId: string, options: {
        startDate?: Date;
        endDate?: Date;
        limit?: number;
    } = {}): Promise<{ unlocks: UnlockSummary[], totalCreditsUsed: number, successfulUnlocks: number }> => {
        const { startDate, endDate, limit = 100 } = options;

        const queryBuilder = this.getRepository()
            .createQueryBuilder('unlock')
            .select([
                'unlock.leadId',
                'unlock.unlockedAt',
                'unlock.unlockType',
                'unlock.creditsUsed',
                'unlock.isSuccessful'
            ])
            .where('unlock.workspaceId = :workspaceId', { workspaceId })
            .andWhere('unlock.unlockedBy = :userId', { userId });

        if (startDate) {
            queryBuilder.andWhere('unlock.unlockedAt >= :startDate', { startDate });
        }
        
        if (endDate) {
            queryBuilder.andWhere('unlock.unlockedAt <= :endDate', { endDate });
        }

        const unlocks = await queryBuilder
            .orderBy('unlock.unlockedAt', 'DESC')
            .limit(limit)
            .getMany();

        const totalCreditsUsed = unlocks.reduce((sum, unlock) => sum + unlock.creditsUsed, 0);
        const successfulUnlocks = unlocks.filter(unlock => unlock.isSuccessful).length;

        return {
            unlocks: unlocks.map(unlock => ({
                leadId: unlock.leadId,
                unlockedAt: unlock.unlockedAt,
                unlockType: unlock.unlockType,
                creditsUsed: unlock.creditsUsed,
                isSuccessful: unlock.isSuccessful
            })),
            totalCreditsUsed,
            successfulUnlocks
        };
    }

    getWorkspaceUnlockStats = async (workspaceId: string, options: {
        startDate?: Date;
        endDate?: Date;
    } = {}): Promise<{
        totalUnlocks: number;
        successfulUnlocks: number;
        totalCreditsUsed: number;
        unlocksByType: Record<UnlockType, number>;
        unlocksBySource: Record<UnlockSource, number>;
    }> => {
        const { startDate, endDate } = options;

        const queryBuilder = this.getRepository()
            .createQueryBuilder('unlock')
            .where('unlock.workspaceId = :workspaceId', { workspaceId });

        if (startDate) {
            queryBuilder.andWhere('unlock.unlockedAt >= :startDate', { startDate });
        }
        
        if (endDate) {
            queryBuilder.andWhere('unlock.unlockedAt <= :endDate', { endDate });
        }

        const unlocks = await queryBuilder.getMany();

        const totalUnlocks = unlocks.length;
        const successfulUnlocks = unlocks.filter(unlock => unlock.isSuccessful).length;
        const totalCreditsUsed = unlocks.reduce((sum, unlock) => sum + unlock.creditsUsed, 0);

        const unlocksByType: Record<UnlockType, number> = {
            [UnlockType.Email]: 0,
            [UnlockType.Phone]: 0,
            [UnlockType.Full]: 0,
            [UnlockType.Partial]: 0
        };

        const unlocksBySource: Record<UnlockSource, number> = {
            [UnlockSource.Manual]: 0,
            [UnlockSource.Bulk]: 0,
            [UnlockSource.API]: 0,
            [UnlockSource.Workflow]: 0
        };

        unlocks.forEach(unlock => {
            unlocksByType[unlock.unlockType]++;
            unlocksBySource[unlock.unlockSource]++;
        });

        return {
            totalUnlocks,
            successfulUnlocks,
            totalCreditsUsed,
            unlocksByType,
            unlocksBySource
        };
    }

    bulkCreateUnlocks = async (unlockData: CreateLeadUnlockData[]): Promise<LeadUnlock[]> => {
        try {
            const unlocks = unlockData.map(data => ({
                ...data,
                creditsUsed: data.creditsUsed || 1,
                isSuccessful: data.isSuccessful !== false,
                unlockedAt: data.unlockedAt || new Date()
            }));
            
            return await this.batchAdd(unlocks);
        } catch (err) {
            throw new ServerProcessingError(err.message);
        }
    }

    markUnlockAsExpired = async (unlockId: string): Promise<void> => {
        await this.update({ id: unlockId }, {
            expiresAt: new Date()
        });
    }

    cleanupExpiredUnlocks = async (): Promise<void> => {
        const now = new Date();
        
        await this.getRepository()
            .createQueryBuilder()
            .softDelete()
            .where('expiresAt IS NOT NULL')
            .andWhere('expiresAt < :now', { now })
            .execute();
    }

    findByWorkspaceAndLeadId = async (workspaceId: string, leadId: string): Promise<LeadUnlock | null> => {
        return await this.findOne({ 
            workspaceId, 
            leadId 
        });
    }

    updateUnlock = async (unlockId: string, updateData: Partial<CreateLeadUnlockData>): Promise<boolean> => {
        return await this.update({ id: unlockId }, updateData);
    }

    isLeadUnlocked = async (workspaceId: string, leadId: string): Promise<boolean> => {
        const unlock = await this.findByWorkspaceAndLeadId(workspaceId, leadId);
        return unlock !== null && unlock.isSuccessful;
    }

    getUnlockCreditsUsed = async (workspaceId: string, userId: string, startDate?: Date, endDate?: Date): Promise<number> => {
        const queryBuilder = this.getRepository()
            .createQueryBuilder('unlock')
            .select('SUM(unlock.creditsUsed)', 'totalCredits')
            .where('unlock.workspaceId = :workspaceId', { workspaceId })
            .andWhere('unlock.unlockedBy = :userId', { userId });

        if (startDate) {
            queryBuilder.andWhere('unlock.unlockedAt >= :startDate', { startDate });
        }
        
        if (endDate) {
            queryBuilder.andWhere('unlock.unlockedAt <= :endDate', { endDate });
        }

        const result = await queryBuilder.getRawOne();
        return parseInt(result.totalCredits) || 0;
    }

    findUnlocksByDateRange = async (workspaceId: string, startDate: Date, endDate: Date): Promise<LeadUnlock[]> => {
        const queryBuilder = this.getRepository()
            .createQueryBuilder('unlock')
            .where('unlock.workspaceId = :workspaceId', { workspaceId })
            .andWhere('unlock.unlockedAt >= :startDate', { startDate })
            .andWhere('unlock.unlockedAt <= :endDate', { endDate });
        
        return await queryBuilder.getMany();
    }
}
