globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[domain]/lead-generation/people/saved-search/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/global-error.tsx":{"*":{"id":"(ssr)/./src/app/global-error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/people/find-leads/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/people/find-leads/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/layout.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/router.tsx":{"*":{"id":"(ssr)/./src/providers/router.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/components/landing/registerReferral.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/components/landing/registerReferral.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/components/tracking.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/components/tracking.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/providers/alert.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/providers/broadcast.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/providers/broadcast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/providers/preview.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/providers/preview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/providers/screenSize.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/providers/screenSize.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../packages/ui/src/providers/user.tsx":{"*":{"id":"(ssr)/../../packages/ui/src/providers/user.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/people/saved-search/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/people/saved-search/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/companies/my-leads/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/companies/my-leads/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/companies/find-leads/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/companies/find-leads/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/companies/saved-search/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/companies/saved-search/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/people/my-leads/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/people/my-leads/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/notes/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/notes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[domain]/lead-generation/people/details/[leadId]/page.tsx":{"*":{"id":"(ssr)/./src/app/[domain]/lead-generation/people/details/[leadId]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\global-error.tsx":{"id":"(app-pages-browser)/./src/app/global-error.tsx","name":"*","chunks":["app/global-error","static/chunks/app/global-error.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\people\\find-leads\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/people/find-leads/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\layout.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/layout.tsx","name":"*","chunks":["app/[domain]/layout","static/chunks/app/%5Bdomain%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\providers\\router.tsx":{"id":"(app-pages-browser)/./src/providers/router.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\components\\landing\\registerReferral.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/components/landing/registerReferral.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\components\\tracking.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/components/tracking.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\providers\\alert.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/providers/alert.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\providers\\broadcast.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/providers/broadcast.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\providers\\preview.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/providers/preview.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\providers\\screenSize.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/providers/screenSize.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\packages\\ui\\src\\providers\\user.tsx":{"id":"(app-pages-browser)/../../packages/ui/src/providers/user.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\node_modules\\.pnpm\\next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.16_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1_sass@1.89.2/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/page.tsx","name":"*","chunks":["app/[domain]/page","static/chunks/app/%5Bdomain%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\people\\saved-search\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/people/saved-search/page.tsx","name":"*","chunks":["app/[domain]/lead-generation/people/saved-search/page","static/chunks/app/%5Bdomain%5D/lead-generation/people/saved-search/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\companies\\my-leads\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/companies/my-leads/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\companies\\find-leads\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/companies/find-leads/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\companies\\saved-search\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/companies/saved-search/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\people\\my-leads\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/people/my-leads/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\notes\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/notes/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\people\\details\\[leadId]\\page.tsx":{"id":"(app-pages-browser)/./src/app/[domain]/lead-generation/people/details/[leadId]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\global-error":[],"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\layout":["static/css/app/[domain]/layout.css"],"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\page":["static/css/app/[domain]/page.css"],"C:\\Users\\<USER>\\Desktop\\dev\\opendashboard-mono\\apps\\frontend\\src\\app\\[domain]\\lead-generation\\people\\saved-search\\page":["static/css/app/[domain]/lead-generation/people/saved-search/page.css"]}}